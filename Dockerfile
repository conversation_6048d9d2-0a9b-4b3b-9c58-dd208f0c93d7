# Build Stage
FROM node:16-alpine as build-stage

LABEL maintainer="<EMAIL>" \
      version="1.1"

# Set the working directory in the container
WORKDIR /vue-ui

ENV application=file-upload

# Define build arguments
ARG SERVER_NAME
ARG configuration=production

# Copy nginx configuration template
COPY nginx.conf /etc/nginx/nginx.conf.template
COPY security-headers.conf /etc/nginx/security-headers.conf

# Update nginx configuration with dynamic values
RUN echo "SERVER_NAME inside Docker: ${SERVER_NAME}" && \
    sed -i "s|__SERVER_NAME__|${SERVER_NAME}|g" /etc/nginx/nginx.conf.template && \
    mv /etc/nginx/nginx.conf.template /etc/nginx/nginx.conf

# Copy the package.json files to the container
COPY package*.json ./

# Install Vue CLI globally and dependencies
RUN npm install -g @vue/cli @vue/cli-service eslint && \
    npm ci --legacy-peer-deps && \
    npm cache clean --force

# Copy the rest of the application's files to the container
COPY . .

# Build the Application
RUN npm run build

# Production Stage
FROM nginx:alpine as production-stage

# Copy the modified nginx configuration
COPY --from=build-stage /etc/nginx/nginx.conf /etc/nginx/nginx.conf
COPY --from=build-stage /etc/nginx/security-headers.conf /etc/nginx/security-headers.conf

# Copy the built application from the build stage
COPY --from=build-stage /vue-ui/dist /usr/share/nginx/html

RUN mkdir -p /etc/letsencrypt/live

# Expose ports 80 and 443
EXPOSE 80 443

# Run nginx in the foreground
CMD ["nginx", "-g", "daemon off;"]
