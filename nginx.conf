user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Optimization for upstream servers
    proxy_headers_hash_max_size 1024;
    proxy_headers_hash_bucket_size 128;
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main buffer=512k flush=1m;

    # Basic optimization
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 100;
    reset_timedout_connection on;
    client_body_timeout 10;
    send_timeout 2;
    client_max_body_size 10m;

    server {
        listen 443 ssl;
        listen [::]:443 ssl;
        listen 80;
        server_name __SERVER_NAME__;

        root /usr/share/nginx/html;
        index index.html;

        # SSL Configuration
        ssl_certificate /etc/letsencrypt/live/__SERVER_NAME__/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/__SERVER_NAME__/privkey.pem;

        # SSL optimization
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;
        ssl_buffer_size 4k;

        # SSL security
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';

        # Security headers
        include /etc/nginx/security-headers.conf;

        #####################################
        #        Gzip Settings              #
        #####################################
        gzip on;
        gzip_http_version 1.1;
        gzip_disable "MSIE [1-6]\.";
        gzip_min_length 256;
        gzip_vary on;
        gzip_proxied expired no-cache no-store private auth;
        gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss;
        gzip_comp_level 9;
        gzip_buffers 32 8k;

        server_tokens off;

        # Static files caching
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, no-transform";
        }

        # JSON and HTML files
        location ~ /(index.html|.+\.(json))$ {
            expires -1;
            add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
        }

        location / {
            root /usr/share/nginx/html;  # or the correct path where your files are located

            # Proxy headers
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;

            proxy_set_header X-Forwarded-Proto https;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Host $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_ssl_session_reuse off;
            proxy_redirect off;

            try_files $uri $uri/ /index.html;

            add_header Set-Cookie "name=value; SameSite=Strict;";
            add_header Cache-Control "public, max-age=31536000";
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            expires off;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # HTTP redirect
    server {
        listen 80;
        listen [::]:80;
        server_name __SERVER_NAME__;

        # Redirect all HTTP traffic to HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }
}