{"name": "data-ui", "version": "1.0.0", "private": true, "scripts": {"serve": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --port 8081", "build": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "prettier": "npx prettier --write ."}, "dependencies": {"@fortawesome/fontawesome-free": "^6.5.1", "@uppy/core": "^3.13.1", "@uppy/dashboard": "^3.9.1", "@vue/compat": "^3.3.4", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "axios": "^1.6.4", "bootstrap": "^4.6.2", "bootstrap-vue": "^2.23.1", "core-js": "^3.35.0", "country-state-city": "^3.2.1", "crypto-js": "^4.2.0", "jquery": "^3.7.1", "keycloak-js": "^23.0.7", "leaflet": "^1.9.4", "localforage": "^1.10.0", "loglevel": "^1.9.1", "register-service-worker": "^1.7.2", "vue": "^3.3.4", "vue-plugin-load-script": "^2.1.1", "vue-router": "^4.2.5", "vuex": "^4.1.0", "vuex-persist": "^3.1.3"}, "devDependencies": {"@babel/eslint-parser": "^7.23.3", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-pwa": "^5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.5", "babel-eslint": "^10.1.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "worker-loader": "^3.0.8"}}