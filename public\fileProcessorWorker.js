self.onmessage = async function (event) {
  const { batch, uploadObj, API_FTP_URL } = event.data;
  try {
    const processedFiles = await Promise.all(batch.map(file => processFile(file)));
    const uploadResults = await uploadFiles(processedFiles, uploadObj, API_FTP_URL);
    self.postMessage({ success: true, results: uploadResults });
  } catch (error) {
    self.postMessage({ success: false, error: error.message });
  }
};

async function processFile(file) {
  try {
    const content = await readFileAsBase64(file);
    return {
      filename: file.name.toLowerCase().replace(/\s/g, '_'),
      content: content.split('base64,')[1],
    };
  } catch (error) {
    throw new Error(`File processing error: ${error.message}`);
  }
}

function readFileAsBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = event => resolve(event.target.result);
    reader.onerror = () => reject(new Error('Failed to read file.'));
    reader.readAsDataURL(file);
  });
}

async function uploadFiles(files, uploadObj, apiEndPoint) {
  try {
    const response = await fetch(apiEndPoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...uploadObj,
        files,
      }),
    });
    if (!response.ok) {
      throw new Error('Failed to upload batch');
    }
    return response.json();
  } catch (error) {
    throw new Error(`Upload failed: ${error.message}`);
  }
}
