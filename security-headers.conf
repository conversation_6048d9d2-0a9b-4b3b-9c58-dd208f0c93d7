# This header tells the browser to only access the website over HTTPS, not HTTP. The max-age parameter specifies how long the browser should remember this setting.
add_header Strict-Transport-Security "max-age=31449600" always;

# This header is used to prevent cross-site scripting (XSS) and other code injection attacks by specifying which sources of content are allowed to be loaded by the browser when the website is accessed.
add_header Content-Security-Policy "default-src 'self'; font-src 'self' data:" always;

add_header X-XSS-Protection "1; mode=block" always;

# X-Frame-Options: This header tells the browser not to allow the website to be displayed in an <iframe> or <frame> on another website.
add_header X-Frame-Options "DENY" always;

# X-Content-Type-Options: This header tells the browser to disable content-sniffing and only use the MIME type given in the Content-Type header.
add_header X-Content-Type-Options "nosniff" always;

# Referrer-Policy: This header tells the browser how to send the Referer header when following links.
add_header Referrer-Policy "strict-origin" always;

# Feature-Policy: This header tells the browser which features and APIs the website is allowed to use, such as the microphone, geolocation, and camera.
add_header Feature-Policy "microphone 'none'; geolocation 'none'; camera 'none'" always;

# Permissions-Policy: This header tells the browser which permissions the website is allowed to request for certain features and APIs
add_header Permissions-Policy "camera=(), geolocation=(), microphone=()" always;