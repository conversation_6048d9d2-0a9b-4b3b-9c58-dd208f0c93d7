<template>
  <div id="app">
    <div class="logo-container" v-if="isSpinnerShowing">
      <b-spinner label="Loading..." class="spinner"></b-spinner>
      <b-img :src="imageURL" rounded thumbnail fluid></b-img>
    </div>
    <router-view />
  </div>
</template>
<script>
export default {
  computed: {
    isSpinnerShowing() {
      return this.$store.state.isSpinnerShowing;
    },
    imageURL() {
      return this.$store.state.userStore.loggedInUser?.sidebar_logo;
    },
  },
  methods: {
    handleToast({ message, title, variant }) {
      this.$bvToast.toast(message, {
        title: title,
        autoHideDelay: 3000,
        appendToast: false,
        variant: variant,
      });
    },
  },
  created() {
    document.documentElement.style.setProperty('--primary', process.env.VUE_APP_PRIMARY_COLOR);
  },
  mounted() {
    this.$root.$on('showToast', toastData => {
      this.handleToast(toastData);
    });
  },
};
</script>
<style></style>
