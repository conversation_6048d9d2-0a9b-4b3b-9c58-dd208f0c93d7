@import url(~@fortawesome/fontawesome-free/css/all.css);
.custom-switch,
.pointer,
a {
  cursor: pointer;
}
@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('@/assets/fonts/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('@/assets/fonts/pxiEyp8kv8JHgFVrJJfecg.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('@/assets/fonts/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('@/assets/fonts/pxiByp8kv8JHgFVrLEj6Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('@/assets/fonts/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
    U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF,
    U+FFFD;
}
:root {
  --primary: darkcyan;
  --dark: #343a40;
  --light: #f0f0f0;
  --white: #ffffff;
  --grey: #888;
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: var(--white);
  border-radius: 10px;
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: var(--white);
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  cursor: pointer;
  background-color: var(--primary);
}
* {
  margin: 0;
  padding: 0;
  border: 0;
}
body {
  height: 100vh !important;
  overflow-x: hidden;
}
#app,
.leaflet-container,
body {
  font-family: Poppins, sans-serif !important;
  font-size: 14px !important;
}
.text-primary,
.text-primaryBg,
a {
  color: var(--primary) !important;
}
.font-weight-bold,
label {
  font-weight: 600 !important;
}
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after,
.primaryBg {
  background-color: var(--primary) !important;
}
.leaflet-right .leaflet-control {
  margin-right: 5px;
}
.leaflet-bottom .leaflet-control {
  margin-bottom: 5px;
}
.leaflet-popup-content {
  margin: 5px;
  line-height: 1.5;
  font-size: 14px;
}
.leaflet-control-zoom {
  background: var(--grey);
  padding: 5px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.card-body,
.table td,
.table th {
  padding: 0.5rem !important;
}
.leaflet-control-scale-line {
  box-sizing: border-box;
  background: var(--white);
  text-shadow: 1px 1px var(--white);
  font-size: 14px;
  font-weight: 600;
  border: solid 2px var(--primary) !important;
  border-radius: 5px;
}
.leaflet-control-scale {
  bottom: 5px !important;
  right: 4rem !important;
}
.leaflet-container a.leaflet-popup-close-button {
  width: 30px !important;
  height: 30px !important;
  font-size: 32px !important;
  border-radius: 10px;
  background: var(--white) !important;
}
a.text-primary:focus,
a.text-primary:hover {
  color: var(--dark) !important;
}
.b-table-sticky-header {
  max-height: calc(100vh - 180px) !important;
}
.b-table-sticky-header > .table.b-table > thead > tr > th {
  position: sticky !important;
}
.b-sidebar:not(.b-sidebar-right) {
  left: 55px !important;
  top: 0;
  right: auto;
  background: var(--white) !important;
  min-width: 220px;
  border-radius: 0;
  max-width: 270px !important;
  height: 100%;
}
.title {
  color: var(--light);
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s !important;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.dropdown-toggle,
.page-item.active .page-link {
  background-color: var(--grey) !important;
}
.sideBarList {
  margin: 10px !important;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}
.shadow-lg {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  border-radius: 15px;
}
.alert,
.card-body {
  margin: 0 0.5rem !important;
}
.modal-title {
  font-weight: 600;
}
.modal-body,
.modal-header {
  padding: 0.5rem 1rem !important;
  align-items: center;
}
.card-header {
  padding: 0.75rem !important;
}
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(5px);
  z-index: 9999;
}
.logo-container img {
  position: absolute;
  height: 100px;
  border-radius: 20px;
}
.logo-container .spinner {
  width: 15rem !important;
  height: 15rem !important;
  animation-duration: 0.8s !important;
  border-width: 0.7rem;
  color: var(--primary);
}
.home-section {
  position: relative;
  left: 70px;
  width: calc(100% - 70px);
  transition: 0.5s;
}
.project-sidebar.open ~ .home-section {
  left: 395px;
  width: calc(100% - 395px);
}
.table-bordered thead th {
  background: var(--primary);
  color: var(--white);
  font-size: 15px;
}
.btn-primary,
.clickedSideNav,
.select-date .btn,
.sidenav {
  background: var(--grey) !important;
}
.dropdown-menu.show {
  overflow-y: auto;
  max-height: 200px;
  min-width: 11rem !important;
}
.dropdown-menu > li > a,
.dropdown-menu > li > button {
  font-size: 14px !important;
  padding: 5px !important;
  text-align: center;
  color: var(--primary) !important;
}
.dropdown-menu > li > a:active,
.dropdown-menu > li > a:focus,
.dropdown-menu > li > button:active,
.dropdown-menu > li > button:focus {
  background-color: var(--primary) !important;
  color: var(--white) !important;
}
.trimmer {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.page-link {
  color: var(--primary) !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  border: 2px solid #6c757d !important;
}
.page-item.disabled .page-link {
  color: #6c757d !important;
}
.page-item.active .page-link {
  border-color: var(--grey) !important;
  color: var(--white) !important;
}
.custom-switch {
  position: relative;
  display: inline-block;
  width: 7rem;
  height: 2rem;
  margin: 0;
}
.custom-switch input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}
.switch-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--grey);
  border-radius: 1rem;
  transition: background-color 0.3s ease-in-out;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.switch-slider::before {
  content: '';
  position: absolute;
  height: 1.5rem;
  width: 1.5rem;
  left: 0.25rem;
  bottom: 0.25rem;
  background-color: var(--white);
  border-radius: 50%;
  transition: transform 0.3s ease-in-out;
}
.switch-text {
  font-weight: 700;
  font-size: 1rem;
  color: var(--white);
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  position: absolute;
  width: 70%;
  text-align: center;
}
.switch-text-left {
  left: 0;
  opacity: 0;
}
.switch-text-right {
  right: 0;
  opacity: 1;
}
.custom-switch input:checked + .switch-slider {
  background-color: var(--primary);
  animation: 0.4s ease-out pulse;
}
.custom-switch input:checked + .switch-slider::before {
  transform: translateX(5rem);
}
.custom-switch input:checked + .switch-slider .switch-text-left {
  opacity: 1;
  transform: translateX(0.5rem);
}
.custom-switch input:checked + .switch-slider .switch-text-right {
  opacity: 0;
  transform: translateX(-0.5rem);
}
.custom-switch:hover .switch-slider::before {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}
.custom-switch input:checked:hover + .switch-slider::before {
  transform: translateX(5rem) scale(1.1);
}
.custom-switch input:disabled + .switch-slider {
  background: #6c757d !important;
  cursor: not-allowed;
}
.custom-switch input:disabled + .switch-slider:before {
  background: #999;
}
.custom-switch input:disabled ~ .switch-text {
  color: #999;
}
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 139, 139, 0.5);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(0, 139, 139, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 139, 139, 0);
  }
}
#print-template {
  visibility: hidden;
  position: fixed;
}
.leaflet-cluster-anim .leaflet-marker-icon,
.leaflet-cluster-anim .leaflet-marker-shadow {
  -webkit-transition: -webkit-transform 0.3s ease-out, opacity 0.3s ease-in;
  -moz-transition: -moz-transform 0.3s ease-out, opacity 0.3s ease-in;
  -o-transition: -o-transform 0.3s ease-out, opacity 0.3s ease-in;
  transition: transform 0.3s ease-out, opacity 0.3s ease-in;
}
.leaflet-cluster-spider-leg {
  -webkit-transition: -webkit-stroke-dashoffset 0.3s ease-out, -webkit-stroke-opacity 0.3s ease-in;
  -moz-transition: -moz-stroke-dashoffset 0.3s ease-out, -moz-stroke-opacity 0.3s ease-in;
  -o-transition: -o-stroke-dashoffset 0.3s ease-out, -o-stroke-opacity 0.3s ease-in;
  transition: stroke-dashoffset 0.3s ease-out, stroke-opacity 0.3s ease-in;
}
.marker-cluster-small {
  background-color: rgba(181, 226, 140, 0.6);
}
.marker-cluster-small div {
  background-color: rgba(110, 204, 57, 0.6);
}
.marker-cluster-medium {
  background-color: rgba(241, 211, 87, 0.6);
}
.marker-cluster-medium div {
  background-color: rgba(240, 194, 12, 0.6);
}
.marker-cluster-large {
  background-color: rgba(253, 156, 115, 0.6);
}
.marker-cluster-large div {
  background-color: rgba(241, 128, 23, 0.6);
}
.leaflet-oldie .marker-cluster-small {
  background-color: #b5e28c;
}
.leaflet-oldie .marker-cluster-small div {
  background-color: #6ecc39;
}
.leaflet-oldie .marker-cluster-medium {
  background-color: #f1d357;
}
.leaflet-oldie .marker-cluster-medium div {
  background-color: #f0c20c;
}
.leaflet-oldie .marker-cluster-large {
  background-color: #fd9c73;
}
.leaflet-oldie .marker-cluster-large div {
  background-color: #f18017;
}
.marker-cluster {
  background-clip: padding-box;
  border-radius: 20px;
}
.marker-cluster div {
  width: 30px;
  height: 30px;
  margin-left: 5px;
  margin-top: 5px;
  text-align: center;
  border-radius: 15px;
  font: 12px 'Helvetica Neue', Arial, Helvetica, sans-serif;
}
.marker-cluster span {
  line-height: 30px;
}
@media (max-width: 767px) {
  .home-section,
  .project-sidebar.open ~ .home-section {
    left: 0;
    width: 100%;
  }
  .home-section {
    padding: var(--mobile-padding);
  }
  .btn,
  .form-control,
  .form-select,
  button {
    min-height: var(--touch-target-min);
    font-size: 16px;
  }
  .btn,
  button {
    padding: 0.75rem 1rem;
  }
  .form-control,
  .form-select {
    padding: 0.75rem;
  }
  .card-body,
  .modal-body,
  .modal-header {
    padding: 1rem !important;
  }
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
  }
  .table-responsive {
    border: none;
  }
  .table {
    font-size: 14px;
  }
  .table td,
  .table th {
    padding: 0.5rem 0.25rem !important;
    white-space: nowrap;
  }
  .card {
    margin-bottom: 1rem;
    border-radius: 8px;
  }
  .card-body {
    margin: 0 !important;
  }
  .mobile-hidden {
    display: none !important;
  }
  .mobile-full-width {
    width: 100% !important;
  }
  .mobile-text-center {
    text-align: center !important;
  }
  .mobile-no-padding {
    padding: 0 !important;
  }
  .mobile-small-padding {
    padding: 0.5rem !important;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .home-section {
    left: 70px;
    width: calc(100% - 70px);
  }
  .project-sidebar.open ~ .home-section {
    left: 280px;
    width: calc(100% - 280px);
  }
  .tablet-hidden {
    display: none !important;
  }
}
@media (min-width: 1024px) {
  .desktop-hidden {
    display: none !important;
  }
}
@media (hover: none) and (pointer: coarse) {
  .btn,
  .dropdown-toggle,
  .form-control,
  .nav-link,
  .page-link,
  button {
    min-height: 48px;
    min-width: 48px;
  }
  .fa,
  .fab,
  .far,
  .fas,
  a {
    min-height: 44px;
    display: inline-flex;
  }
  .fa,
  .fab,
  .far,
  .fas {
    padding: 0.5rem;
    min-width: 44px;
    align-items: center;
    justify-content: center;
  }
  .btn:hover,
  .card:hover,
  .project-card:hover {
    transform: none;
    box-shadow: inherit;
  }
  .scrollable-menu,
  .sidebar-content {
    -webkit-overflow-scrolling: touch;
  }
  a {
    align-items: center;
  }
  .btn:focus,
  .dropdown-toggle:focus,
  .form-control:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo-container img {
    image-rendering: -webkit-optimize-contrast;
  }
}
@media (max-width: 767px) and (orientation: landscape) {
  .modal-dialog {
    max-height: 90vh;
    overflow-y: auto;
  }
  .home-section {
    padding: 0.5rem;
  }
}
@media print {
  #print-map,
  #print-template {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
  }
  body * {
    visibility: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  #print-template,
  #print-template * {
    visibility: visible !important;
  }
  #print-template {
    display: flex !important;
    flex-direction: column !important;
    padding: 5px !important;
    box-sizing: border-box !important;
    background-color: #fff !important;
  }
  .print-header {
    flex: 0 0 auto !important;
    margin-bottom: 10px !important;
    border-bottom: 2px solid #333 !important;
    padding-bottom: 10px !important;
  }
  .header-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
  }
  .print-map-container {
    flex: 1 1 auto !important;
    position: relative !important;
    width: 100% !important;
    margin: 0 !important;
    border: 1px solid #ccc !important;
    page-break-inside: avoid !important;
    overflow: hidden !important;
  }
  #print-map {
    background: #f9f9f9 !important;
  }
  .print-footer {
    flex: 0 0 auto !important;
    margin-top: 10px !important;
    padding-top: 10px !important;
    border-top: 2px solid #333 !important;
    text-align: center !important;
  }
  .leaflet-control,
  .leaflet-control-container,
  .leaflet-popup,
  .mobile-print-hidden {
    display: none !important;
  }
  @page {
    size: A4 landscape !important;
    margin: 0.5cm;
  }
}
