<template>
  <div class="map-layout home-section">
    <div ref="myMap" class="w-100 h-100"></div>
    <div class="control-container">
      <div class="timeline" v-if="isMeasure">
        <b-dropdown>
          <template #button-content>
            {{ selectedBoundaryLabel }}
          </template>
          <b-dropdown-item-button
            v-for="(item, index) in processedData"
            :key="index"
            @click="updateKmlLayer(item)">
            {{ item.key }}
          </b-dropdown-item-button>
        </b-dropdown>
      </div>
      <div class="marker-toggle">
        <label class="custom-switch" v-b-tooltip.hover title="Toggle Image Overlay">
          <input type="checkbox" v-model="showMarkers" @change="toggleMarkers()" />
          <span class="switch-slider">
            <span class="switch-text switch-text-left"><i class="fa fa-eye mr-1"></i>Show</span>
            <span class="switch-text switch-text-right">
              <i class="fa fa-eye-slash mr-1"></i>Hide
            </span>
          </span>
        </label>
        <label
          class="custom-switch"
          v-b-tooltip.hover
          title="Toggle Marker Display"
          v-show="showMarkers">
          <input type="checkbox" v-model="showClusteredMarkers" @change="renderMarkers()" />
          <span class="switch-slider">
            <span class="switch-text switch-text-left">Cluster</span>
            <span class="switch-text switch-text-right">Simple</span>
          </span>
        </label>
      </div>
      <div class="satellite-view">
        <b-button
          class="btn-primary shadow"
          @click="updateTileLayer()"
          v-b-tooltip.hover.left
          title="Toggle Map Layer">
          <i class="fa fa-satellite" v-if="terrain"></i>
          <i class="fa fa-mountain-sun" v-else></i>
        </b-button>
      </div>
      <div class="weather">
        <div class="weather-modal" v-if="showWeather"><weather /></div>
        <b-button
          v-b-tooltip.hover.left
          title="Weather"
          class="btn-primary"
          @click="showWeather = !showWeather">
          <i class="fa fa-temperature-high"></i>
        </b-button>
      </div>
      <div class="full-screen">
        <b-button
          v-b-tooltip.hover.left
          title="Full Screen"
          class="btn-primary"
          @click="toggleFullscreen()">
          <i :class="fullscreenIcon"></i>
        </b-button>
      </div>
      <div class="reset-map">
        <b-button
          v-b-tooltip.hover.left
          title="Reset Map"
          class="btn-primary"
          @click="resetMapView()">
          <i class="fa fa-arrows-alt"></i>
        </b-button>
      </div>
      <!-- <div class="geo-location">
        <b-button
          v-b-tooltip.hover.left
          title="My Location"
          class="btn-primary"
          @click="getUserLocation()">
          <i class="fa fa-map-location-dot"></i>
        </b-button>
      </div> -->
      <div class="export-map">
        <b-button
          v-b-tooltip.hover.left
          title="Export Map"
          class="btn-primary"
          @click="exportMap()">
          <i class="fa fa-camera"></i>
        </b-button>
      </div>
    </div>
    <b-modal
      size="md"
      id="selectBlockModal"
      title="Block Selector for Overlay"
      hide-footer
      centered
      no-close-on-esc
      no-close-on-backdrop
      hide-header-close>
      <div class="d-flex justify-content-center flex-column align-items-center text-center">
        <b-form inline class="my-3 w-100 justify-content-center">
          <label class="mr-3">Select block:</label>
          <b-form-select v-model="block" class="w-50">
            <b-form-select-option :value="null" disabled>Select block</b-form-select-option>
            <b-form-select-option value="all">All</b-form-select-option>
            <b-form-select-option v-for="block in blocksList" :key="block" :value="block">
              {{ block }}
            </b-form-select-option>
          </b-form-select>
        </b-form>
        <div class="my-2 w-100">
          <b-button class="shadow mr-5" @click="closeModal()" variant="danger">Close</b-button>
          <b-button class="shadow primaryBg" @click="renderMarkers()" :disabled="!block">
            Submit
          </b-button>
        </div>
      </div>
    </b-modal>
    <div id="print-template" class="print-only">
      <div class="print-header">
        <div class="header-content">
          <h5>Project Name: {{ projectData.name }}</h5>
          <h5>Flight Date: {{ projectData.flight_date }}</h5>
        </div>
      </div>
      <div class="print-map-container">
        <div id="print-map"></div>
      </div>
      <div class="print-footer">
        <p>Generated by © Datasee.AI at {{ dateFormat(new Date()) }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import '@/assets/plugins/leaflet-edgebuffer';
import '@/assets/plugins/leaflet-markercluster';
import omnivore from '@/assets/plugins/leaflet-omnivore';
import '@/assets/plugins/leaflet-twofingerzoom';
import { formatCustomDate } from '@/store/constant';
import L from 'leaflet';
import { toRaw } from 'vue';
import { mapGetters } from 'vuex';

const weather = () => import('@/components/modules/home/<USER>');

export default {
  name: 'airmap',
  components: {
    weather,
  },
  data() {
    return {
      url: process.env.VUE_APP_TERRAIN_TILE_LAYER,
      satelliteUrl: process.env.VUE_APP_SATTELITE_TILE_LAYER,
      proxyUrl: `${process.env.VUE_APP_API_MEASURE_URL}project/proxy/?url=`,
      options: {
        attributionControl: false,
        zoomControl: false,
        minZoom: 2,
        maxZoom: 23,
        zoomSnap: 0.5,
        twoFingerZoom: true,
        maxBoundsViscosity: 1.0,
        maxBounds: [
          [-90, -180],
          [90, 180],
        ],
      },
      terrain: false,
      showWeather: false,
      currentMarkers: [],
      showMarkers: false,
      block: null,
      selectedBoundaryLabel: '',
      fullscreen: false,
      mapControls: {},
      tileLayer: null,
      map: null,
      kmlLayer: null,
      markerClusterLayer: null,
      markerSimpleLayer: null,
      showClusteredMarkers: false,
    };
  },
  computed: {
    ...mapGetters({
      projectData: 'homeStore/getProjectData',
      projectMetadata: 'homeStore/getProjectMetadata',
      isThermography: 'homeStore/isThermography',
      displayedImages: 'homeStore/getDisplayedImages',
    }),
    blocksList() {
      return [...new Set(this.displayedImages.map(img => img.block).filter(Boolean))];
    },
    fullscreenIcon() {
      return this.fullscreen
        ? 'fa fa-down-left-and-up-right-to-center'
        : 'fa fa-up-right-and-down-left-from-center';
    },
    dateFormat() {
      return originalDate => {
        return formatCustomDate(originalDate);
      };
    },
    isMeasure() {
      return this.projectData.portal === 'measure';
    },
    processedData() {
      return (
        this.projectData?.processed_data?.map(item => ({
          value: item.boundary_file_location,
          key: `${item.date} (${item.type})`,
        })) || []
      );
    },
    center() {
      const center = this.projectData?.center;
      if (!center) return [0, 0];
      return center.split(',').map(coord => parseFloat(coord.trim()));
    },
    zoom() {
      return this.projectData?.zoom_level ?? 2;
    },
  },
  methods: {
    async exportMap() {
      try {
        this.$root.$emit('showToast', {
          message: 'Please wait while we are preparing for screenshot',
          title: 'Capturing Screenshot..',
          variant: 'info',
        });
        const printMapContainer = document.querySelector('#print-map');
        printMapContainer.innerHTML = '';

        // Create and append a new map container
        const printMapDiv = Object.assign(document.createElement('div'), {
          className: 'print-map-content',
          style: 'width: 100%; height: 100%;',
        });
        printMapContainer.appendChild(printMapDiv);

        // Clone the existing map and append it
        printMapDiv.appendChild(this.$refs.myMap.cloneNode(true));
        await new Promise(resolve => setTimeout(resolve, 2000));
        this.$refs.myMap.invalidateSize?.();

        window.print();
        printMapContainer.innerHTML = '';
      } catch (error) {
        this.$root.$emit('showToast', {
          message: 'Failed to capture screenshot',
          title: 'Error',
          variant: 'danger',
        });
      }
    },
    toggleFullscreen() {
      try {
        const elem = document.documentElement;
        this.fullscreen = !this.fullscreen;

        if (this.fullscreen) {
          if (elem.requestFullscreen) elem.requestFullscreen();
          else if (elem.webkitRequestFullscreen) elem.webkitRequestFullscreen();
          else if (elem.msRequestFullscreen) elem.msRequestFullscreen();
        } else {
          if (document.exitFullscreen) document.exitFullscreen();
          else if (document.webkitExitFullscreen) document.webkitExitFullscreen();
          else if (document.msExitFullscreen) document.msExitFullscreen();
        }
      } catch (error) {
        this.$root.$emit('showToast', {
          message: 'Try again later or contact support',
          title: error.message,
          variant: 'danger',
        });
      }
    },
    getUserLocation() {
      if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(
          position => {
            const userLat = position.coords.latitude;
            const userLng = position.coords.longitude;
            const userMarker = L.marker([userLat, userLng]).addTo(toRaw(this.map));
            userMarker.bindPopup('Your Location').openPopup();
            this.map.flyTo([userLat, userLng], 20);
          },
          error => {
            this.$root.$emit('showToast', {
              message: `Try again later or contact support`,
              title: error.message,
              variant: 'danger',
            });
          },
          {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
          }
        );
      } else {
        this.$root.$emit('showToast', {
          message: 'Geolocation is not available in this browser.',
          title: 'Error Occured',
          variant: 'danger',
        });
      }
    },
    resetMapView() {
      this.map.flyTo(this.center, this.zoom);
    },
    initMap() {
      this.map = L.map(this.$refs.myMap, {
        ...this.options,
        center: this.center,
        zoom: this.zoom,
      });

      const rawMap = toRaw(this.map);

      L.control.scale({ position: 'bottomright', imperial: true, metric: true }).addTo(rawMap);
      L.control.zoom({ position: 'bottomright' }).addTo(rawMap);
      this.updateTileLayer();

      this.kmlLayer = new L.FeatureGroup().addTo(rawMap);
      this.markerSimpleLayer = new L.FeatureGroup().addTo(rawMap);
      this.markerClusterLayer = new L.markerClusterGroup().addTo(rawMap);

      if (this.processedData.length > 0 && this.isMeasure) {
        this.updateKmlLayer(this.processedData[0]);
      } else {
        this.updateKmlLayer();
      }
    },
    updateTileLayer() {
      this.terrain = !this.terrain;
      if (this.tileLayer) {
        this.map.removeLayer(this.tileLayer);
      }
      this.tileLayer = L.tileLayer(this.terrain ? this.url : this.satelliteUrl, {
        minZoom: 2,
        maxZoom: 23,
        edgeBufferTiles: 1,
      });
      this.tileLayer.addTo(toRaw(this.map));
    },
    renderGeoData(urls, hex) {
      urls.forEach(url => {
        const fullUrl = this.proxyUrl + encodeURIComponent(url);
        const customLayer = L.geoJson(null, {
          style: () => ({ color: hex }),
        });
        const dataLayer = omnivore
          .kml(fullUrl, null, customLayer)
          .on('ready', () => {
            if (dataLayer.getLayers().length) this.kmlLayer.addLayer(dataLayer);
          })
          .on('error', () => {
            console.error('Error loading KML file:', url);
          });
      });
    },
    renderMarkers() {
      this.removeMarkers();
      let filteredImages = this.displayedImages;
      if (this.block && this.block !== 'all') {
        filteredImages = this.displayedImages.filter(img => img.block === this.block);
      }
      filteredImages.forEach(img => {
        if (img.latitude && img.longitude) {
          const popupContent = `
        <img src="${img.path}" alt="${img.name}" style="width: 300px; height: auto; border-radius: 10px;"/>
        <p style="margin: 5px; font-weight: 700; text-align: center;">${img.name}</p>
      `;
          const marker = L.marker([img.latitude, img.longitude]).bindPopup(popupContent);
          if (this.showClusteredMarkers) {
            marker.addTo(toRaw(this.markerClusterLayer));
          } else {
            marker.addTo(toRaw(this.markerSimpleLayer));
          }
          this.currentMarkers.push(marker);
        }
      });
      this.$bvModal.hide('selectBlockModal');
    },
    removeMarkers() {
      if (this.markerSimpleLayer) {
        this.markerSimpleLayer.clearLayers();
      }
      if (this.markerClusterLayer) {
        this.markerClusterLayer.clearLayers();
      }
      this.currentMarkers.forEach(marker => marker.remove());
      this.currentMarkers = [];
    },
    toggleMarkers() {
      if (this.showMarkers) {
        this.$bvModal.show('selectBlockModal');
      } else {
        this.removeMarkers();
        this.block = null;
      }
    },
    closeModal() {
      this.$bvModal.hide('selectBlockModal');
      this.showMarkers = false;
      this.block = null;
    },
    updateKmlLayer(data = null) {
      if (this.kmlLayer) this.kmlLayer.clearLayers();
      let boundaryFileLocation = null;
      if (data) {
        this.selectedBoundaryLabel = data.key;
        boundaryFileLocation = data.value;
      } else {
        boundaryFileLocation = this.projectData?.boundary_file_location;
      }
      if (boundaryFileLocation) this.renderGeoData(boundaryFileLocation, '#343a40');
    },
  },
  mounted() {
    this.$store.commit('homeStore/setCurrentPath', 'map');

    this.$nextTick(() => {
      L.Marker.prototype._animateZoom = function (opt) {
        if (!this._map) return;
        const pos = this._map._latLngToNewLayerPoint(this._latlng, opt.zoom, opt.center).round();
        this._setPos(pos);
      };

      L.Popup.prototype._animateZoom = function (opt) {
        if (!this._map) return;
        const pos = this._map._latLngToNewLayerPoint(this._latlng, opt.zoom, opt.center).round();
        const anchor = this._getAnchor();
        L.DomUtil.setPosition(this._container, pos.add(anchor));
      };

      this.initMap();
      const dropdownMenu = this.$el.querySelector('.dropdown-menu');
      if (dropdownMenu) dropdownMenu.addEventListener('wheel', e => e.stopPropagation());
    });
    this.$root.$on('toggleMarkers', () => {
      if (this.showMarkers) {
        this.showMarkers = false;
        this.showClusteredMarkers = false;
        this.block = null;
        this.toggleMarkers();
      }
    });
  },
  beforeUnmount() {
    if (this.map) {
      this.map.remove();
      this.map = null;
    }
    this.$root.$off('toggleMarkers');
  },
};
</script>

<style scoped>
.map-layout {
  position: fixed;
  top: 0px;
  bottom: 0;
  right: 0;
  left: 0px !important;
  width: 100% !important;
}
.control-container {
  position: fixed;
  z-index: 1000;
  right: 3rem;
}
.marker-toggle {
  position: fixed;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
/* Mobile responsive map controls */
@media (max-width: 767px) {
  .map-layout {
    top: 4rem !important; /* Account for mobile hamburger menu */
  }

  .control-container {
    right: 0.5rem;
    top: 4.5rem;
  }

  .marker-toggle {
    top: 4.5rem;
    right: 0.5rem;
    gap: 0.5rem;
  }

  .custom-switch {
    width: 5rem;
    height: 1.5rem;
    font-size: 0.75rem;
  }

  .switch-text {
    font-size: 0.7rem;
  }

  .full-screen {
    bottom: 7rem;
  }

  .reset-map {
    bottom: 0.5rem;
  }

  .satellite-view {
    bottom: 9rem;
  }

  .weather {
    bottom: 11rem;
  }

  .weather-modal {
    right: 1rem;
    top: 6rem;
    max-width: calc(100vw - 2rem);
  }

  .export-map {
    bottom: 13rem;
  }

  .timeline {
    bottom: 5px;
    right: 4rem;
    font-size: 0.875rem;
  }

  .timeline .dropdown-toggle {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  /* Modal adjustments for mobile */
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
  }

  .modal-body {
    padding: 1rem 0.5rem;
  }

  /* Button adjustments */
  .btn-primary {
    min-height: 44px;
    min-width: 44px;
    padding: 0.5rem;
  }

  .btn-primary i {
    font-size: 16px;
  }
}
.full-screen {
  position: absolute;
  bottom: 9rem;
}
.reset-map {
  position: absolute;
  bottom: 0.5rem;
}
.satellite-view {
  position: absolute;
  bottom: 12rem;
}
.weather {
  position: absolute;
  bottom: 15rem;
}
.weather-modal {
  position: fixed;
  right: 3.5rem;
  top: 8rem;
}
.export-map {
  position: absolute;
  bottom: 18rem;
}
.geo-location {
  position: absolute;
  bottom: 24rem;
}
.timeline {
  position: absolute;
  bottom: 5px;
  right: 8rem;
}
</style>
