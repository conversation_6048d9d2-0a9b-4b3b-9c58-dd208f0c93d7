<template>
  <b-form>
    <b-form-row>
      <b-col cols="4">
        <b-form-group label="Name:" label-for="name-input">
          <b-form-input id="name-input" v-model="name" disabled></b-form-input>
        </b-form-group>
      </b-col>
      <b-col cols="4">
        <b-form-group label="Location:" label-for="location-input">
          <b-form-input id="location-input" :value="location" disabled class="text-capitalize">
          </b-form-input>
        </b-form-group>
      </b-col>
      <b-col cols="4">
        <b-form-group label="Category:" label-for="category-input">
          <b-form-input id="category-input" v-model="category" disabled></b-form-input>
        </b-form-group>
      </b-col>
    </b-form-row>
    <b-form-row>
      <b-col cols="4" v-if="localProject.plant_capacity">
        <b-form-group label="Capacity:" label-for="capacity-input">
          <b-form-input
            id="capacity-input"
            v-model="localProject.plant_capacity"
            disabled></b-form-input>
        </b-form-group>
      </b-col>
      <b-col cols="4" v-if="localProject.plant_area">
        <b-form-group label="Area:" label-for="area-input">
          <b-form-input id="area-input" v-model="localProject.plant_area" disabled></b-form-input>
        </b-form-group>
      </b-col>
      <b-col cols="4">
        <b-form-group label="Select Flight Date*:" label-for="flight-date">
          <b-input-group id="flight-date">
            <template #prepend>
              <b-input-group-text><i class="fa fa-calendar"></i></b-input-group-text>
            </template>
            <b-form-input type="date" v-model="date" :max="getCurrentDate"></b-form-input>
          </b-input-group>
          <b-form-invalid-feedback :state="validateState()">
            Flight Date is required
          </b-form-invalid-feedback>
        </b-form-group>
      </b-col>
    </b-form-row>
    <div class="w-100 text-center">
      <b-button class="shadow mr-3" variant="danger" @click="closeModal()">Close</b-button>
      <b-button class="primaryBg shadow" @click="setProjectData()" :disabled="!date">
        Submit
      </b-button>
    </div>
  </b-form>
</template>

<script>
import { formatCategoryName, formatProjectName } from '@/store/constant';
import { useVuelidate } from '@vuelidate/core';
import { required } from '@vuelidate/validators';

export default {
  name: 'createProject',
  props: {
    selectedProjectProp: Object,
  },
  setup() {
    return { v$: useVuelidate() };
  },
  data() {
    return {
      localProject: { ...this.selectedProjectProp },
      date: null,
    };
  },
  validations() {
    return {
      date: {
        required,
      },
    };
  },
  computed: {
    getCurrentDate() {
      const today = new Date();
      let month = today.getMonth() + 1;
      let day = today.getDate();
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;
      return `${today.getFullYear()}-${month}-${day}`;
    },
    name() {
      const { name } = this.localProject || {};
      return formatProjectName(name);
    },
    category() {
      const { category } = this.localProject || {};
      return formatCategoryName(category);
    },
    location() {
      const { city, state, country } = this.localProject || {};
      return [city, state, country].filter(Boolean).join(', ');
    },
  },
  methods: {
    validateState() {
      const { $invalid, $dirty } = this.v$.date;
      return $invalid ? $dirty : null;
    },
    setProjectData() {
      const obj = { ...this.localProject, selected: true, flight_date: this.date };
      this.$store.commit('homeStore/setProjectData', obj);
      this.$bvModal.hide('selectProjectModal');
    },
    closeModal() {
      this.$store.commit('homeStore/setProjectData', {});
      this.$bvModal.hide('selectProjectModal');
    },
  },
};
</script>

<style scoped></style>
