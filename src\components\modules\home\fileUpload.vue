<template>
  <div class="px-2 mt-3 home-section text-center">
    <b-tabs
      card
      justified
      active-nav-item-class="font-weight-bold text-primary"
      @activate-tab="onTabSwitch">
      <b-tab active title="File Upload">
        <div class="d-flex align-items-center justify-content-around mb-3 file-upload-controls">
          <div class="control-group">
            <b-form inline class="mobile-form">
              <label class="my-0 mx-2 mobile-label" for="custom-select">File Type*:</label>
              <b-form-select
                id="custom-select"
                v-model="selected"
                :options="file_options"
                @change="resetData()"
                :disabled="!projectData.flight_date">
              </b-form-select>
            </b-form>
          </div>
          <div class="control-group">
            <b-button
              class="primaryBg mx-2 shadow"
              @click="uploadFiles()"
              :disabled="isUploading || !isFilesAdded">
              <span v-if="!isUploading"><i class="fa fa-upload"></i> </span>
              <span v-else>
                <div class="spinner-border spinner-border-sm" role="status"></div>
              </span>
            </b-button>
            <b-button
              variant="danger"
              @click="resetData()"
              class="shadow"
              :disabled="isUploading || !projectData.flight_date"
              v-b-tooltip.hover.bottom
              title="Reset">
              <i class="fa fa-arrows-rotate"></i>
            </b-button>
          </div>
          <div class="text-center font-weight-bold status-alerts">
            <b-alert class="m-0" v-if="!projectData.flight_date" show variant="warning">
              Select Project Data
            </b-alert>
            <b-alert
              class="m-0"
              v-else-if="projectData.flight_date && !isUploading && !formItems[0].count"
              show
              variant="warning">
              Select File Type
            </b-alert>
            <b-alert class="m-0" v-else-if="isUploading" show variant="info">
              <div class="d-flex align-items-center justify-content-center">
                <div class="mr-2">Uploading Started</div>
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
              </div>
            </b-alert>
            <b-alert class="m-0" v-else-if="formItems[0].count" show variant="success">
              {{ formItems[0].count }} {{ formItems[0].count === 1 ? 'file' : 'files' }} uploaded
              successfully
            </b-alert>
          </div>
        </div>
        <div class="d-flex align-items-center justify-content-center flex-column">
          <b-progress
            v-if="progress"
            class="my-2 w-50"
            variant="success"
            striped
            animated
            show-progress
            height="1.5rem">
            <b-progress-bar :value="progressPercentage">
              <span>
                Progress:
                <strong>{{ progressPercentage }} / 100 </strong>
              </span>
            </b-progress-bar>
          </b-progress>
          <div ref="uppyDashboard"></div>
        </div>
      </b-tab>
      <b-tab lazy title="Image Upload">
        <div class="d-flex align-items-center justify-content-around mb-3">
          <h5 class="m-0 font-weight-bolder">Nomenclature of Blocks</h5>
          <b-button
            variant="danger"
            @click="resetItem()"
            class="shadow"
            :disabled="!projectData.flight_date"
            v-b-tooltip.hover.right
            title="Reset">
            <i class="fa fa-arrows-rotate"></i>
          </b-button>
        </div>
        <b-form>
          <div
            v-for="(item, index) in formItems"
            :key="index"
            class="d-flex align-items-center justify-content-between font-weight-bold">
            <div class="w-50">
              <b-form-row>
                <b-col cols="1">
                  <p class="m-0 mt-4">{{ index + 1 }}.</p>
                </b-col>
                <b-col cols="5">
                  <b-form-group label="Image Type*:" label-for="image-type" class="text-left">
                    <b-form-select
                      id="image-type"
                      v-model="item.type"
                      :options="image_options"
                      :disabled="
                        isUploading ||
                        item.isDisabled ||
                        !projectData.flight_date ||
                        !isThermography
                      "></b-form-select>
                  </b-form-group>
                </b-col>
                <b-col cols="6">
                  <b-form-group
                    label="Block/Misson Name*:"
                    label-for="block-name"
                    class="text-left">
                    <b-input-group id="block-name">
                      <template #append>
                        <b-input-group-text>
                          <i class="fa fa-circle-info pointer" id="tooltip-target"></i>
                          <b-tooltip
                            target="tooltip-target"
                            variant="light"
                            triggers="hover"
                            varient="light">
                            <div class="tooltip-class">
                              <b>Folder Format Examples</b>
                              <p class="p-0 m-0 text-left">1. Mission1</p>
                              <p class="p-0 m-0 text-left">2. Mission1/F1</p>
                              <p class="p-0 m-0 text-left">3. Mission1/F2</p>
                              <p class="p-0 m-0 text-left">4. Block1</p>
                            </div>
                          </b-tooltip>
                        </b-input-group-text>
                      </template>
                      <b-form-input
                        v-model="item.mission_name"
                        type="text"
                        placeholder="Enter Block/Misson Name"
                        :disabled="
                          isUploading || item.isDisabled || !item.type || !projectData.flight_date
                        ">
                      </b-form-input>
                    </b-input-group>
                  </b-form-group>
                </b-col>
              </b-form-row>
            </div>
            <b-modal
              :id="'modal-' + index"
              size="lg"
              hide-footer
              centered
              no-close-on-esc
              no-close-on-backdrop>
              <template #modal-header="{ cancel }">
                <h5 class="m-0 p-0">
                  Folder Name -
                  <span class="font-weight-bold">{{ item.mission_name }}</span>
                </h5>
                <div>
                  <b-button
                    variant="danger"
                    @click="cancel()"
                    class="shadow mr-3"
                    :disabled="isUploading">
                    Close
                  </b-button>
                  <b-button
                    class="shadow primaryBg"
                    @click="uploadImages(index)"
                    :disabled="isUploading || item.isDisabled">
                    <span v-if="!isUploading">
                      <i class="fa fa-upload"></i>
                      Upload
                    </span>
                    <span v-else>
                      <div class="spinner-border spinner-border-sm" role="status"></div>
                      Uploading...
                    </span>
                  </b-button>
                </div>
              </template>
              <b-progress
                v-if="progress"
                class="mb-3"
                variant="success"
                striped
                animated
                show-progress
                height="1.5rem">
                <b-progress-bar :value="progressPercentage">
                  <span>
                    Progress:
                    <strong>{{ progressPercentage }} / 100 </strong>
                  </span>
                </b-progress-bar>
              </b-progress>
              <div id="uppyContainer"></div>
            </b-modal>
            <div class="w-25">
              <b-button
                class="shadow mx-2 primaryBg"
                @click="openModal(index)"
                :disabled="
                  !item.mission_name ||
                  this.isUploading ||
                  item.isDisabled ||
                  !projectData.flight_date
                ">
                <i class="fa fa-up-right-from-square"></i>
              </b-button>
              <b-button
                v-if="index > 0"
                variant="danger"
                @click="removeItem(index)"
                class="shadow mx-2 danger"
                :disabled="this.isUploading || item.isDisabled || !projectData.flight_date"
                v-b-tooltip.hover.top
                title="Remove the row">
                <i class="fa fa-trash"></i>
              </b-button>
              <b-button
                v-if="index === formItems.length - 1"
                variant="warning"
                @click="addItem()"
                class="shadow"
                v-b-tooltip.hover.top
                :disabled="this.isUploading || !projectData.flight_date"
                title="Add the row">
                <i class="fa fa-plus"></i>
              </b-button>
            </div>
            <div class="w-25">
              <template v-if="!item.count">
                <b-alert v-if="!projectData.flight_date" show variant="warning">
                  Select Project Data
                </b-alert>
                <b-alert v-else-if="!item.type" show variant="warning"> Select Image Type </b-alert>
                <b-alert v-else-if="!item.mission_name" show variant="warning">
                  Enter folder or block name
                </b-alert>
                <b-alert v-else show variant="warning"> Click on arrow to upload files </b-alert>
              </template>
              <template v-else>
                <b-alert show variant="success">
                  {{ item.count }} {{ item.count === 1 ? 'file' : 'files' }} uploaded successfully
                </b-alert>
              </template>
            </div>
          </div>
        </b-form>
      </b-tab>
    </b-tabs>
  </div>
</template>

<script>
import log from '@/services/logger';
import { fileFormats, IMAGE_TYPE } from '@/store/constant';
import Uppy from '@uppy/core';
import Dashboard from '@uppy/dashboard';

import '@uppy/core/dist/style.css';
import '@uppy/dashboard/dist/style.css';
import { mapGetters } from 'vuex';

const MAX_CONCURRENT_UPLOADS = parseInt(process.env.VUE_APP_FTP_MAX_CONCURRENT_UPLOADS);
const MAX_BATCH_SIZE_IN_MB = parseInt(process.env.VUE_APP_FTP_MAX_BATCH_SIZE_IN_MB);
const API_FTP_URL = `${process.env.VUE_APP_API_FTP_URL}s3/files`;

export default {
  name: 'fileUpload',
  data() {
    return {
      uppy: null,
      progress: null,
      isUploading: false,
      isFilesAdded: false,
      selected: 'KML',
      formItems: [],
      file_options: [
        { value: null, text: 'Select file type', disabled: true },
        ...Object.keys(fileFormats).filter(format => format !== IMAGE_TYPE.RGB),
      ],
      image_options: [
        { value: null, text: 'Select image type', disabled: true },
        ...Object.keys(IMAGE_TYPE),
      ],
    };
  },
  computed: {
    userData() {
      return this.$store.state.userStore.loggedInUser;
    },
    ...mapGetters({
      projectData: 'homeStore/getProjectData',
      isThermography: 'homeStore/isThermography',
      projectsArray: 'homeStore/getProjectsArray',
    }),
    getAllowedExtensions() {
      return fileFormats[this.selected];
    },
    progressPercentage() {
      if (this.progress) {
        const { totalFiles, processedFiles } = this.progress;
        return ((processedFiles / totalFiles) * 100).toFixed(2);
      }
      return 0;
    },
  },
  methods: {
    async getDataInitialize() {
      const urlParams = new URLSearchParams(window.location.search);
      const encodedData = urlParams.get('data');
      if (encodedData) {
        const decodedData = atob(decodeURIComponent(encodedData));
        const { project_id, portal } = JSON.parse(decodedData);

        if (project_id && this.projectsArray.length) {
          const project = this.projectsArray.find(
            project => project.id === project_id && project.portal == portal
          );
          if (project) {
            project.selected = true;
            this.$store.commit('homeStore/setProjectData', project);
            this.$bvModal.show('selectProjectModal');
          }
        }
        const url = new URL(window.location);
        url.searchParams.delete('data');
        url.searchParams.delete('customToken');
        history.replaceState({}, document.title, url);
      } else if (Object.keys(this.projectData).length && !this.projectData.flight_date) {
        this.$bvModal.show('selectProjectModal');
      }
    },
    onTabSwitch() {
      if (!this.isUploading) this.resetData();
    },
    createUppyInstance(allowedFileTypes, target) {
      const uppy = new Uppy({
        autoProceed: false,
        restrictions: {
          allowedFileTypes,
        },
      }).use(Dashboard, {
        inline: true,
        note: `Accepted formats: ${allowedFileTypes}`,
        target,
        showProgressDetails: true,
        proudlyDisplayPoweredByUppy: false,
        centerSingleFile: true,
        hideUploadButton: true,
        hidePauseResumeButton: true,
        hideRetryButton: true,
        hideProgressAfterFinish: true,
        fileManagerSelectionType: 'both',
        width: window.innerWidth < 768 ? '100%' : 750,
        height: window.innerWidth < 768 ? 250 : 400,
      });
      return uppy;
    },
    initializeUppy() {
      const filesList = [];
      this.uppy = this.createUppyInstance(this.getAllowedExtensions, this.$refs.uppyDashboard);
      this.uppy.on('files-added', files => {
        filesList.push(...files);
        this.isFilesAdded = filesList.length > 0;
      });
      this.uppy.on('file-removed', () => {
        filesList.pop();
        this.isFilesAdded = filesList.length > 0;
      });
    },
    addUppyInstance(index) {
      this.formItems[index].uppyItem = this.createUppyInstance(
        fileFormats[IMAGE_TYPE.RGB],
        '#uppyContainer'
      );
    },
    showToast(title, message, variant) {
      this.$root.$emit('showToast', {
        title,
        message,
        variant,
      });
    },
    openModal(index) {
      this.progress = null;
      this.$bvModal.show(`modal-${index}`);
      this.$nextTick(() => {
        this.addUppyInstance(index);
      });
    },
    resetData() {
      this.uppy.cancelAll();
      this.uppy.setOptions({
        restrictions: {
          allowedFileTypes: this.getAllowedExtensions,
        },
      });
      this.uppy.getPlugin('Dashboard').setOptions({
        note: `Accepted formats: ${this.getAllowedExtensions}`,
        disabled: false,
      });
      this.isFilesAdded = false;
      this.progress = null;
      this.resetItem();
    },
    resetItem() {
      this.formItems = [];
      this.addItem();
    },
    addItem() {
      this.formItems.push({
        isDisabled: false,
        type: this.isThermography ? null : IMAGE_TYPE.RGB,
        mission_name: null,
        uppyItem: null,
        count: null,
      });
    },
    removeItem(index) {
      this.formItems.splice(index, 1);
    },
    checkValidity(files, uploadObj) {
      if (!files || files.length === 0) {
        this.showToast('Files not selected', 'Select valid files to upload', 'danger');
        return false;
      }
      if (!uploadObj.flight_date) {
        this.showToast('Date not selected', 'Select valid flight date', 'danger');
        return false;
      }
      if (!uploadObj.bucket_name || !uploadObj.project_name) {
        this.showToast('Invalid Bucket/Project Name', 'Please try again', 'danger');
        return false;
      }
      return true;
    },
    createMetadataAsFile(obj) {
      const metadata = {
        user_name: this.userData.name,
        portal: this.projectData.portal,
        project_id: this.projectData.id,
        project_name: this.projectData.name,
        project_location: `${this.projectData.city}, ${this.projectData.state}, ${this.projectData.country}`,
        flight_date: this.projectData.flight_date,
        bucket_name: this.userData.bucket,
        folder_path: `${obj.project_name}/${obj.flight_date}/${obj.mission_name}/`,
      };
      const metadataFile = new File([JSON.stringify(metadata)], 'metadata.json', {
        type: 'application/json',
      });
      return metadataFile;
    },
    createUploadObject(formItem) {
      function formatString(str) {
        return str.toLowerCase().replace(/[ -]/g, '_');
      }
      const project_name = `${formatString(this.projectData.name)}-${formatString(
        this.projectData.country
      )}`;
      return {
        mission_name: formItem.mission_name,
        project_name: project_name,
        flight_date: this.projectData.flight_date,
        bucket_name: this.userData.bucket,
      };
    },
    createBatches(files) {
      const batchSizeLimit = MAX_BATCH_SIZE_IN_MB * 1024 * 1024;
      let batches = [];
      let currentBatch = [];
      let currentBatchSize = 0;
      files.forEach(file => {
        if (currentBatchSize + file.size > batchSizeLimit) {
          batches.push(currentBatch);
          currentBatch = [];
          currentBatchSize = 0;
        }
        currentBatch.push(file);
        currentBatchSize += file.size;
      });
      if (currentBatch.length > 0) {
        batches.push(currentBatch);
      }
      return batches;
    },
    async uploadFiles() {
      const formItem = {
        mission_name: this.selected,
        uppyItem: this.uppy,
      };
      const files = formItem.uppyItem.getFiles().map(file => file.data);
      const uploadObj = this.createUploadObject(formItem);

      if (!this.checkValidity(files, uploadObj)) {
        return;
      }
      this.isFilesAdded = false;
      formItem.uppyItem.getPlugin('Dashboard').setOptions({ disabled: true });
      await this.processAndUpload(files, uploadObj, 0, false);
    },
    async uploadImages(index) {
      const formItem = this.formItems[index];
      const files = formItem.uppyItem.getFiles().map(file => file.data);
      const uploadObj = this.createUploadObject(formItem);

      if (!this.checkValidity(files, uploadObj)) {
        return;
      }
      uploadObj.mission_name = `${formItem.type}/${uploadObj.mission_name}`;
      formItem.uppyItem.getPlugin('Dashboard').setOptions({ disabled: true });
      await this.processAndUpload(files, uploadObj, index, true);
    },
    async processAndUpload(files, uploadObj, index, metadata) {
      this.setUploadingState(true);
      const batches = this.createBatches(files);
      this.progress.totalBatches = batches.length;
      this.progress.totalFiles = files.length;
      this.progress.completedBatches = 0;

      log.info(
        `Starting uploading process for Project: ${uploadObj.project_name}, Flight Date: ${uploadObj.flight_date}, Total Files: ${this.progress.totalFiles}, Total Batches: ${this.progress.totalBatches}`
      );
      try {
        await this.uploadBatchesInParallel(batches, uploadObj);
        if (metadata) {
          const metadataFile = this.createMetadataAsFile(uploadObj);
          await this.uploadBatch([metadataFile], uploadObj, batches.length + 1, false);
        }
        this.showToast(
          `${this.progress.processedFiles} Files uploaded successfully`,
          'All the selected files are uploaded',
          'success'
        );
        this.formItems[index].count = this.progress.processedFiles;
        this.formItems[index].isDisabled = true;
      } catch (error) {
        log.info('Error occured during upload:', error);
        this.showToast('Error Occured', error.message, 'danger');
      } finally {
        this.setUploadingState(false);
      }
    },
    async uploadBatchesInParallel(batches, uploadObj) {
      const uploadQueue = batches.map(
        (batch, index) => () => this.uploadBatch(batch, uploadObj, index + 1)
      );
      while (uploadQueue.length > 0) {
        const batchesToUpload = uploadQueue.splice(0, MAX_CONCURRENT_UPLOADS);
        const uploadPromises = batchesToUpload.map(uploadFn => uploadFn());
        await Promise.all(uploadPromises);
      }
    },
    async uploadBatch(batch, uploadObj, batchNumber, isBatchCount = true) {
      return new Promise((resolve, reject) => {
        if (isBatchCount)
          log.info(`Processing batch ${batchNumber} of ${this.progress.totalBatches}`);
        const worker = new Worker('/fileProcessorWorker.js');
        worker.onmessage = event => {
          const { success, results, error } = event.data;
          if (success) {
            if (isBatchCount) this.updateProgress(results, batchNumber);
            resolve(results);
          } else {
            log.info(`Batch upload failed: ${error}`);
            reject(new Error(error));
          }
          worker.terminate();
        };
        worker.postMessage({ batch, uploadObj, API_FTP_URL });
      });
    },
    updateProgress(results, batchNumber) {
      const successfulUploads = results.successful_uploads || [];
      this.progress.processedFiles += successfulUploads.length;
      this.progress.completedBatches++;
      log.info(
        `Batch ${batchNumber} uploaded successfully. Files in batch: ${successfulUploads.length}. ` +
          `Total progress: ${this.progress.processedFiles}/${this.progress.totalFiles} files, ` +
          `${this.progress.completedBatches}/${this.progress.totalBatches} batches`
      );
    },
    setUploadingState(isUploading) {
      this.isUploading = isUploading;
      if (isUploading) {
        this.progress = {
          totalFiles: 0,
          processedFiles: 0,
          currentBatch: 0,
          totalBatches: 0,
        };
      }
    },
  },
  async mounted() {
    this.$store.commit('homeStore/setCurrentPath', 'file-upload');
    if (this.projectData && this.projectData.id) this.projectData.flight_date = null;

    this.getDataInitialize();
    this.initializeUppy();
  },
  created() {
    this.addItem();
  },
  beforeUnmount() {
    if (this.uppy) this.uppy.cancelAll();
    this.formItems.forEach(instance => {
      if (instance.uppyItem) instance.uppyItem.cancelAll();
    });
  },
  watch: {
    projectData(newValue) {
      if (newValue && newValue.flight_date) {
        this.resetData();
      } else {
        this.uppy.getPlugin('Dashboard').setOptions({ disabled: true });
      }
    },
  },
};
</script>

<style scoped>
.tooltip-class {
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

/* Mobile responsive styles for file upload */
@media (max-width: 767px) {
  .home-section {
    padding: 0.5rem !important;
    margin-top: 4rem !important; /* Account for mobile hamburger menu */
  }

  .file-upload-controls {
    flex-direction: column !important;
    gap: 1rem;
    align-items: stretch !important;
  }

  .control-group {
    width: 100%;
  }

  .mobile-form {
    flex-direction: column !important;
    align-items: stretch !important;
  }

  .mobile-label {
    margin: 0 0 0.5rem 0 !important;
    text-align: left;
    font-weight: bold;
  }

  .status-alerts {
    width: 100% !important;
  }

  /* Button adjustments */
  .btn {
    min-height: 44px;
    margin: 0.25rem !important;
  }

  /* Form select adjustments */
  .form-control,
  .form-select {
    min-height: 44px;
    font-size: 16px;
    width: 100%;
  }

  /* Progress bar mobile adjustments */
  .my-2.w-50 {
    width: 100% !important;
    margin: 1rem 0 !important;
  }

  /* Tab content adjustments */
  .tab-content {
    padding: 1rem 0.5rem;
  }

  /* Form adjustments for Image Upload tab */
  .d-flex.align-items-center.justify-content-between {
    flex-direction: column;
    align-items: stretch !important;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f9f9f9;
  }

  .w-50,
  .w-25 {
    width: 100% !important;
  }

  /* Mobile form row improvements */
  .b-form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .b-form-row .col-1 {
    order: -1;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: var(--primary);
    margin-bottom: 0.5rem;
  }

  .b-form-row .col-5,
  .b-form-row .col-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  /* Button container improvements */
  .w-25:last-child {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .w-25:nth-last-child(2) {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
  }

  .w-25 .btn {
    min-width: 44px;
    min-height: 44px;
  }

  /* Button groups */
  .d-flex.align-items-center.justify-content-around {
    flex-direction: column;
    gap: 1rem;
  }

  /* Form row adjustments */
  .b-form-row {
    flex-direction: column;
  }

  .b-form-row .col-1,
  .b-form-row .col-5,
  .b-form-row .col-6 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 1rem;
  }

  /* Modal adjustments */
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
    max-height: calc(100vh - 1rem);
  }

  .modal-content {
    max-height: calc(100vh - 1rem);
    overflow-y: auto;
  }

  .modal-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1rem !important;
    border-bottom: 1px solid #dee2e6;
  }

  .modal-header h5 {
    text-align: center;
    margin-bottom: 0;
    font-size: 16px;
    line-height: 1.3;
  }

  .modal-header div {
    display: flex;
    gap: 0.5rem;
  }

  .modal-header .btn {
    flex: 1;
    min-height: 44px;
    font-size: 14px;
  }

  .modal-body {
    padding: 1rem !important;
  }

  /* Uppy dashboard in modal */
  #uppyContainer .uppy-Dashboard {
    width: 100% !important;
    height: 300px !important;
  }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .home-section {
    padding: 1rem !important;
  }

  /* Adjust Uppy dashboard size for tablets */
  .uppy-Dashboard {
    max-width: 100%;
  }

  .status-alerts {
    width: 30% !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 48px;
    padding: 0.75rem 1rem;
  }

  .form-control,
  .form-select {
    min-height: 48px;
    font-size: 16px;
  }
}
.dot {
  width: 8px;
  height: 8px;
  background-color: var(--dark);
  border-radius: 50%;
  margin: 0 3px;
  opacity: 0;
  animation: pulse 1.5s infinite;
}
@keyframes pulse {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.5);
  }
  25% {
    opacity: 1;
    transform: scale(1);
  }
}
.dot:nth-child(1) {
  animation-delay: 0s;
}
.dot:nth-child(2) {
  animation-delay: 0.5s;
}
.dot:nth-child(3) {
  animation-delay: 1s;
}
</style>
