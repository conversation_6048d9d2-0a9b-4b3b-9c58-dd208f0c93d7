<template>
  <div class="px-3 mt-3 home-section text-center">
    <div class="d-flex justify-content-between align-items-center">
      <div class="d-flex align-items-center mb-3 mr-2">
        <label class="mr-2 mb-0">Items per page:</label>
        <b-form-select
          v-model="itemsPerPage"
          :options="perPageOptions"
          class="w-auto"></b-form-select>
      </div>
      <b-form-group style="width: 40%">
        <b-input-group>
          <b-form-input v-model="searchQuery" placeholder="Search.."></b-form-input>
          <b-input-group-append>
            <b-button :disabled="!searchQuery" @click="clearSearch" class="shadow">
              Clear
            </b-button>
          </b-input-group-append>
        </b-input-group>
      </b-form-group>
      <p class="mb-3 font-weight-bold">
        Showing {{ filteredItems.length }} of {{ displayedImages.length }} rows
      </p>
      <label
        class="custom-switch mb-3"
        v-b-tooltip.hover
        title="Toggle Image Type"
        v-show="displayedImages.length">
        <input type="checkbox" v-model="$store.state.homeStore.isRGB" :disabled="!isThermography" />
        <span class="switch-slider">
          <span class="switch-text switch-text-left">RGB</span>
          <span class="switch-text switch-text-right">THERMAL</span>
        </span>
      </label>
    </div>
    <b-table
      bordered
      striped
      hover
      responsive
      show-empty
      sticky-header
      no-border-collapse
      head-variant="dark"
      :items="paginatedItems"
      :fields="getFields"
      @sort-changed="handleSort">
      <template #cell(name)="data">
        <a
          @click="openModal(data.item)"
          class="text-uppercase font-weight-bold text-decoration-none">
          {{ data.value }}
        </a>
      </template>
      <template #cell(block)="data">
        <span class="text-uppercase">{{ data.value }}</span>
      </template>
      <template #cell(size)="data">{{ data.value }} MB</template>
      <template #cell(altitude)="data">{{ data.value }} m</template>
    </b-table>
    <b-pagination
      v-if="filteredItems.length > itemsPerPage"
      v-model="currentPage"
      :total-rows="filteredItems.length"
      :per-page="itemsPerPage"
      first-text="First"
      prev-text="Prev"
      next-text="Next"
      last-text="Last"
      pills
      align="center"></b-pagination>
    <b-modal
      size="lg"
      id="selectImageModal"
      hide-footer
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <template #modal-header="{ cancel }">
        <div class="w-100 d-flex align-items-center justify-content-between">
          <h5 class="font-weight-bold m-0 p-0 text-uppercase">
            {{ modalData.name }}
          </h5>
          <b-button class="shadow" variant="danger" @click="cancel()">Close</b-button>
        </div>
      </template>
      <b-row>
        <b-col cols="8" class="d-flex align-items-center p-2 position-relative">
          <b-skeleton v-show="isImageLoading" animation="fade" class="rounded w-100 h-100">
          </b-skeleton>
          <b-img
            v-show="!isImageLoading"
            thumbnail
            center
            rounded
            fluid-grow
            :src="modalData.path"
            :alt="modalData.name"
            :class="isImageLoading ? 'position-absolute' : ''"
            @load="isImageLoading = false"
            @error="isImageLoading = false">
          </b-img>
        </b-col>
        <b-col cols="4" class="d-flex align-items-center p-2">
          <b-card class="shadow-lg">
            <b-card-text>
              <p class="mb-2 trimmer"><b>Block:</b> {{ modalData.block }}</p>
              <p class="mb-2"><b>Size:</b> {{ modalData.size }} MB</p>
              <p class="mb-2"><b>Latitude:</b> {{ modalData.latitude }}</p>
              <p class="mb-2"><b>Longitude:</b> {{ modalData.longitude }}</p>
              <p class="mb-2"><b>Altitude:</b> {{ modalData.altitude }} m</p>
              <p class="mb-2"><b>Format:</b> {{ modalData.format }}</p>
              <p class="mb-2"><b>Mode:</b> {{ modalData.mode }}</p>
              <p class="mb-2"><b>Make:</b> {{ modalData.make }}</p>
              <p class="mb-2"><b>Model:</b> {{ modalData.model }}</p>
              <p class="mb-2"><b>Software:</b> {{ modalData.software }}</p>
              <p class="mb-2">
                <strong>Resolution:</strong> {{ modalData.width }} x {{ modalData.height }} px
              </p>
              <p class="mb-2"><strong>Orientation:</strong> {{ modalData.orientation }}</p>
              <p class="mb-0"><strong>Datetime:</strong> {{ modalData.datetime }}</p>
            </b-card-text>
          </b-card>
        </b-col>
      </b-row>
    </b-modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'fileUpload',
  components: {},
  data() {
    return {
      currentPage: 1,
      itemsPerPage: 10,
      perPageOptions: [10, 25, 50, 100],
      searchQuery: '',
      sortBy: '',
      sortDesc: false,
      modalData: {},
      isImageLoading: true,
    };
  },
  computed: {
    ...mapGetters({
      projectData: 'homeStore/getProjectData',
      projectMetadata: 'homeStore/getProjectMetadata',
      isThermography: 'homeStore/isThermography',
      displayedImages: 'homeStore/getDisplayedImages',
    }),
    filteredItems() {
      let filtered = this.displayedImages;
      // Apply search filter across all fields
      if (this.searchQuery) {
        const search = this.searchQuery.toLowerCase().trim();
        filtered = filtered.filter(item =>
          Object.values(item).some(value => value?.toString().toLowerCase().includes(search))
        );
      }
      // Apply sorting
      if (this.sortBy) {
        const isDescending = this.sortDesc;
        filtered = filtered.slice().sort((a, b) => {
          const aVal = a[this.sortBy] ?? '';
          const bVal = b[this.sortBy] ?? '';
          const aStr = typeof aVal === 'string' ? aVal.toLowerCase() : String(aVal).toLowerCase();
          const bStr = typeof bVal === 'string' ? bVal.toLowerCase() : String(bVal).toLowerCase();
          return isDescending
            ? bStr.localeCompare(aStr, undefined, { numeric: true })
            : aStr.localeCompare(bStr, undefined, { numeric: true });
        });
      }
      return filtered;
    },
    paginatedItems() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.filteredItems.slice(start, end);
    },
    getFields() {
      const fields = [
        'name',
        'block',
        'type',
        'size',
        'latitude',
        'longitude',
        'altitude',
        'datetime',
      ];
      if (this.displayedImages && this.displayedImages.length) {
        return Object.values(fields).map(key => ({
          key: key,
          sortable: true,
        }));
      }
      return [];
    },
  },
  methods: {
    clearSearch() {
      this.searchQuery = '';
      this.currentPage = 1;
    },
    resetPage() {
      this.currentPage = 1;
    },
    handleSort(ctx) {
      this.sortBy = ctx.sortBy;
      this.sortDesc = ctx.sortDesc;
    },
    openModal(item) {
      this.modalData = item;
      this.$bvModal.show('selectImageModal');
    },
  },
  mounted() {
    this.$store.commit('homeStore/setCurrentPath', 'datasets');
    if (this.projectData && this.projectData.id) this.projectData.flight_date = null;
    this.$store.commit('homeStore/setProjectMetadata', {});
  },
  watch: {
    projectMetadata: 'clearSearch',
    searchQuery: 'resetPage',
    itemsPerPage: 'resetPage',
    'modalData.path': {
      immediate: true,
      handler(newImage) {
        if (newImage) this.isImageLoading = true;
      },
    },
  },
};
</script>

<style scoped></style>
