<template>
  <div class="sidebar-content">
    <b-card class="shadow-lg">
      <b-card-text>
        <div class="location text-center my-1">
          <h4
            class="trimmer font-weight-bolder mr-2"
            v-b-tooltip.hover
            :title="nameFormat(projectData.name)">
            {{ nameFormat(projectData.name) }}
          </h4>
          <i
            class="fa fa-arrow-left text-primary pointer"
            @click="$router.go(-1)"
            v-b-tooltip.hover
            title="Go to Back Page"></i>
        </div>
        <p class="mb-2 trimmer font-weight-bold">
          <i class="fa fa-location-dot text-primary"></i>
          {{ projectData.city }}, {{ projectData.state }}, {{ projectData.country }}
        </p>
        <div class="metrics-grid">
          <div class="metric-item trimmer w-100">
            <i class="fa fa-user"></i>
            <div class="metric-details">
              <span class="metric-label">Uploaded By</span>
              <span class="metric-value">{{ projectMetadata.user_name }}</span>
            </div>
          </div>
          <div class="d-flex justify-content-between align-items-center">
            <div class="metric-item trimmer">
              <i class="fa fa-jet-fighter-up"></i>
              <div class="metric-details">
                <span class="metric-label">Flight Date</span>
                <span class="metric-value">{{ projectMetadata.flight_date }}</span>
              </div>
            </div>
            <div class="metric-item trimmer">
              <i class="fa fa-user"></i>
              <div class="metric-details">
                <span class="metric-label">Category</span>
                <span class="metric-value">{{ categoryFormat(projectData.category) }}</span>
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-between align-items-center">
            <div class="metric-item trimmer" v-if="projectData.plant_area">
              <i class="fa fa-chart-area"></i>
              <div class="metric-details">
                <span class="metric-label">Plant Area</span>
                <span class="metric-value">{{ projectData.plant_area }}</span>
              </div>
            </div>
            <div class="metric-item trimmer" v-if="projectData.plant_capacity">
              <i class="fa fa-warehouse"></i>
              <div class="metric-details">
                <span class="metric-label">Plant Capacity</span>
                <span class="metric-value">{{ projectData.plant_capacity }}</span>
              </div>
            </div>
          </div>
        </div>
      </b-card-text>
    </b-card>
    <b-card class="shadow-lg">
      <b-card-text class="d-flex justify-content-between align-items-center">
        <h2 class="section-title my-2">Image Type</h2>
        <label
          class="custom-switch"
          v-b-tooltip.hover
          title="Toggle Image Type"
          v-show="displayedImages.length">
          <input
            type="checkbox"
            v-model="$store.state.homeStore.isRGB"
            @change="$root.$emit('toggleMarkers')"
            :disabled="!isThermography" />
          <span class="switch-slider">
            <span class="switch-text switch-text-left">RGB</span>
            <span class="switch-text switch-text-right">THERMAL</span>
          </span>
        </label>
      </b-card-text>
    </b-card>
    <b-card class="shadow-lg">
      <b-card-text>
        <h2 class="section-title mt-2 mb-3">Technical Details</h2>
        <div class="tech-details-grid">
          <div v-for="(value, key) in filteredTechnicalDetails" :key="key" class="tech-detail-item">
            <span class="detail-label">{{ key }}</span>
            <span class="detail-value">{{ value }}</span>
          </div>
        </div>
      </b-card-text>
    </b-card>
    <b-card class="shadow-lg">
      <b-card-text>
        <h2 class="section-title mt-2 mb-3">
          Original Images
          <span class="image-count">{{ displayedImages.length }} total</span>
        </h2>
        <div class="image-grid" style="grid-template-columns: repeat(4, 1fr)">
          <div
            v-for="(image, index) in limitedImages"
            :key="index"
            class="image-tile"
            style="height: 60px !important; width: 60px !important"
            @click="openModal(image.name, image.path)">
            <b-skeleton
              v-show="loadingStates[index]"
              animation="fade"
              class="rounded w-100 h-100"></b-skeleton>
            <b-img
              v-show="!loadingStates[index]"
              rounded
              :src="image.path"
              :alt="image.name"
              @load="loadingStates[index] = false"
              @error="loadingStates[index] = false"></b-img>
          </div>
          <div v-show="hasMoreImages" class="image-tile more-images" @click="openModal(null, null)">
            +{{ displayedImages.length - 7 }}
          </div>
        </div>
      </b-card-text>
    </b-card>
    <b-modal
      size="lg"
      id="selectImageModal"
      centered
      hide-footer
      no-close-on-esc
      no-close-on-backdrop>
      <template #modal-header="{}">
        <div class="w-100 d-flex align-items-center justify-content-between">
          <h5 class="font-weight-bold m-0 p-0 text-uppercase">
            {{ selectedImageName ? selectedImageName : 'Original Images' }}
          </h5>
          <div>
            <b-button
              class="shadow mr-3"
              variant="warning"
              @click="closeModal(true)"
              v-show="isBack">
              Back
            </b-button>
            <b-button class="shadow" variant="danger" @click="closeModal()">Close</b-button>
          </div>
        </div>
      </template>
      <div v-if="selectedImageName && selectedImagePath">
        <b-img-lazy
          :src="selectedImagePath"
          :alt="selectedImageName"
          thumbnail
          center
          rounded
          fluid
          height="600"
          width="600"></b-img-lazy>
      </div>
      <div v-else>
        <div class="image-grid" style="grid-template-columns: repeat(6, 1fr)">
          <div
            v-for="(image, index) in paginatedImages"
            :key="index"
            class="image-tile"
            style="height: 116px !important; width: 116px !important"
            @click="openModal(image.name, image.path, true)">
            <b-skeleton
              v-show="loadingStates[index]"
              animation="fade"
              class="rounded w-100 h-100"></b-skeleton>
            <b-img
              v-show="!loadingStates[index]"
              rounded
              :src="image.path"
              :alt="image.name"
              @load="loadingStates[index] = false"
              @error="loadingStates[index] = false"></b-img>
          </div>
        </div>
        <b-pagination
          v-if="displayedImages.length > itemsPerPage"
          v-model="currentPage"
          :total-rows="displayedImages.length"
          :per-page="itemsPerPage"
          first-text="First"
          prev-text="Prev"
          next-text="Next"
          last-text="Last"
          pills
          align="center"
          class="mt-3"></b-pagination>
      </div>
    </b-modal>
  </div>
</template>

<script>
import { formatCategoryName, formatProjectName } from '@/store/constant';
import { mapGetters } from 'vuex';

export default {
  name: 'metadataSideNav',
  data() {
    return {
      selectedImageName: '',
      selectedImagePath: '',
      currentPage: 1,
      itemsPerPage: 18,
      isBack: false,
      loadingStates: [],
    };
  },
  computed: {
    ...mapGetters({
      projectData: 'homeStore/getProjectData',
      projectMetadata: 'homeStore/getProjectMetadata',
      isThermography: 'homeStore/isThermography',
      displayedImages: 'homeStore/getDisplayedImages',
    }),
    limitedImages() {
      return this.displayedImages.slice(0, 7);
    },
    hasMoreImages() {
      return this.displayedImages.length > 7;
    },
    paginatedImages() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.displayedImages.slice(start, end);
    },
    filteredTechnicalDetails() {
      const technicalDetails = this.displayedImages?.[0];
      if (!technicalDetails) return {};

      const keyMapping = {
        format: 'File Format',
        mode: 'Color Mode',
        orientation: 'Image Orientation',
        resolutionunit: 'Resolution Unit',
        software: 'Software Used',
      };
      const mergedDetails = {};
      if (technicalDetails.make && technicalDetails.model) {
        mergedDetails['Device'] = `${technicalDetails.make} ${technicalDetails.model}`;
      }
      if (technicalDetails.height && technicalDetails.width) {
        mergedDetails[
          'Image Dimensions'
        ] = `${technicalDetails.width} x ${technicalDetails.height} px`;
      }
      if (technicalDetails.xresolution && technicalDetails.yresolution) {
        mergedDetails[
          'Resolution (dpi)'
        ] = `${technicalDetails.xresolution} x ${technicalDetails.yresolution}`;
      }
      const filteredDetails = Object.entries(keyMapping)
        .filter(([key]) => technicalDetails[key])
        .map(([key, label]) => [label, technicalDetails[key]]);

      return {
        ...mergedDetails,
        ...Object.fromEntries(filteredDetails),
      };
    },
    categoryFormat() {
      return originalName => {
        return formatCategoryName(originalName);
      };
    },
    nameFormat() {
      return originalName => {
        return formatProjectName(originalName);
      };
    },
  },
  methods: {
    openModal(name, url, isBack = false) {
      if (name && url) {
        this.selectedImageName = name;
        this.selectedImagePath = url;
      }
      this.isBack = isBack;
      this.$bvModal.show('selectImageModal');
    },
    closeModal(isBack = false) {
      this.selectedImageName = null;
      this.selectedImagePath = null;
      if (isBack) {
        this.isBack = false;
      } else {
        this.$bvModal.hide('selectImageModal');
      }
    },
  },
  watch: {
    limitedImages: {
      immediate: true,
      handler(newImages) {
        if (newImages) this.loadingStates = newImages.map(() => true);
      },
    },
    paginatedImages: {
      immediate: true,
      handler(newImages) {
        if (newImages) this.loadingStates = newImages.map(() => true);
      },
    },
  },
};
</script>

<style scoped>
.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100vh;
  overflow-y: scroll;
  padding: 0.2rem;
  border: none;
}
.location {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24px;
}
.location-name {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}
.metric-item {
  display: flex;
  align-items: center;
  padding: 8px;
  gap: 8px;
  background: #f1f5f9;
  border-radius: 8px;
  font-size: 18px;
  color: #64748b;
  width: 49%;
}
.metric-details {
  display: flex;
  flex-direction: column;
}
.metric-label {
  font-size: 10px;
  color: #64748b;
}
.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.image-count {
  font-size: 14px;
  color: #64748b;
  font-weight: normal;
}
.tech-details-grid {
  display: grid;
  gap: 16px;
}
.tech-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.detail-label {
  color: #64748b;
  font-size: 14px;
}
.detail-value {
  color: #1e293b;
  font-size: 14px;
  font-weight: 500;
}
.image-grid {
  display: grid;
  gap: 10px;
}
.image-tile {
  position: relative;
  aspect-ratio: 1;
  cursor: pointer;
  transition: transform 0.2s;
}
.image-tile:hover {
  transform: scale(1.05);
}
.image-tile img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}
.more-images {
  background: rgba(0, 0, 0, 0.6);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  border-radius: 8px;
}
</style>
