<template>
  <div class="home-section" v-if="Object.keys(projectData).length">
    <b-navbar class="nav-box">
      <div class="w-100 d-flex align-items-center justify-content-between">
        <b-navbar-brand>
          <span class="font-weight-bold">Project Name: </span>
          <span>{{ nameFormat(projectData.name) }}</span>
        </b-navbar-brand>
        <b-navbar-brand>
          <span class="font-weight-bold">Category: </span>
          <span>{{ categoryFormat(projectData.category) }}</span>
        </b-navbar-brand>
        <b-navbar-brand v-show="projectData.flight_date">
          <span class="font-weight-bold">Flight Date: </span>{{ projectData.flight_date }}
        </b-navbar-brand>
      </div>
    </b-navbar>
  </div>
</template>

<script>
import { formatCategoryName, formatProjectName } from '@/store/constant';
import { mapGetters } from 'vuex';

export default {
  name: 'navbar',
  computed: {
    ...mapGetters({
      projectData: 'homeStore/getProjectData',
    }),
    categoryFormat() {
      return originalName => {
        return formatCategoryName(originalName);
      };
    },
    nameFormat() {
      return originalName => {
        return formatProjectName(originalName);
      };
    },
  },
};
</script>

<style scoped>
.text-data {
  font-weight: 700;
  font-size: 1.1rem;
  margin: 0.1rem;
}
.navbar-brand {
  font-size: 1rem;
}
.nav-box {
  z-index: 999;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  background-color: var(--white);
}
</style>
