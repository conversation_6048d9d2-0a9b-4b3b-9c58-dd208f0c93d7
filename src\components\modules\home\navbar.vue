<template>
  <div class="home-section" v-if="Object.keys(projectData).length">
    <b-navbar class="nav-box">
      <div class="w-100 navbar-content">
        <!-- Mobile layout - stacked -->
        <div class="d-md-none mobile-navbar">
          <div class="navbar-item">
            <span class="font-weight-bold">Project: </span>
            <span class="text-truncate">{{ nameFormat(projectData.name) }}</span>
          </div>
          <div class="navbar-item">
            <span class="font-weight-bold">Category: </span>
            <span>{{ categoryFormat(projectData.category) }}</span>
          </div>
          <div class="navbar-item" v-show="projectData.flight_date">
            <span class="font-weight-bold">Flight Date: </span>
            <span>{{ projectData.flight_date }}</span>
          </div>
        </div>

        <!-- Desktop layout - horizontal -->
        <div class="d-none d-md-flex align-items-center justify-content-between w-100">
          <b-navbar-brand>
            <span class="font-weight-bold">Project Name: </span>
            <span>{{ nameFormat(projectData.name) }}</span>
          </b-navbar-brand>
          <b-navbar-brand>
            <span class="font-weight-bold">Category: </span>
            <span>{{ categoryFormat(projectData.category) }}</span>
          </b-navbar-brand>
          <b-navbar-brand v-show="projectData.flight_date">
            <span class="font-weight-bold">Flight Date: </span>{{ projectData.flight_date }}
          </b-navbar-brand>
        </div>
      </div>
    </b-navbar>
  </div>
</template>

<script>
import { formatCategoryName, formatProjectName } from '@/store/constant';
import { mapGetters } from 'vuex';

export default {
  name: 'navbar',
  computed: {
    ...mapGetters({
      projectData: 'homeStore/getProjectData',
    }),
    categoryFormat() {
      return originalName => {
        return formatCategoryName(originalName);
      };
    },
    nameFormat() {
      return originalName => {
        return formatProjectName(originalName);
      };
    },
  },
};
</script>

<style scoped>
.text-data {
  font-weight: 700;
  font-size: 1.1rem;
  margin: 0.1rem;
}
.navbar-brand {
  font-size: 1rem;
}
.nav-box {
  z-index: 999;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  background-color: var(--white);
}

/* Mobile navbar styles */
@media (max-width: 767px) {
  .nav-box {
    padding: 0.75rem 1rem;
    margin-top: 4rem; /* Account for mobile hamburger menu */
    border-radius: 0 0 8px 8px;
  }

  .mobile-navbar {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .navbar-item {
    display: flex;
    align-items: flex-start;
    font-size: 14px;
    line-height: 1.3;
    padding: 0.25rem 0;
  }

  .navbar-item span:first-child {
    min-width: 80px;
    margin-right: 0.5rem;
    font-weight: 600;
    color: var(--primary);
  }

  .navbar-item span:last-child {
    flex: 1;
    word-break: break-word;
    color: var(--dark);
  }

  .navbar-item .text-truncate {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100vw - 140px);
  }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .navbar-brand {
    font-size: 0.9rem;
  }

  .navbar-brand span {
    display: block;
  }
}
</style>
