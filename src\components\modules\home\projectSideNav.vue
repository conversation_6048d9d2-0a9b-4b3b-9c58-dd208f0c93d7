<template>
  <div>
    <!-- Mobile project sidebar toggle button -->
    <div
      class="mobile-project-toggle d-md-none"
      @click="toggleMobileProjectSidebar"
      v-show="!toggleSidebar">
      <i class="fa fa-folder-open"></i>
      <span>Projects</span>
    </div>

    <div class="project-sidebar" :class="toggleSidebar ? 'open' : 'close'">
      <i
        class="fa fa-angle-right toggle d-none d-md-block"
        @click="$store.commit('homeStore/setToggleSidebar')"
        v-b-tooltip.hover
        title="Close Sidebar"></i>

      <!-- Mobile close button for project sidebar -->
      <div class="mobile-project-close d-md-none" @click="closeMobileProjectSidebar">
        <i class="fa fa-times"></i>
      </div>
      <div v-if="getCurrentPath == 'map'">
        <metadataSideNav />
      </div>
      <div v-else>
        <div class="d-flex justify-content-center align-items-center">
          <div class="header mx-3">Select Project Data</div>
          <div class="pointer" v-b-tooltip.hover title="Reset" @click="resetData()">
            <i class="fa fa-arrows-rotate"></i>
          </div>
        </div>
        <b-input-group class="my-2">
          <b-input-group-prepend is-text>
            <div><i class="fa fa-magnifying-glass"></i></div>
          </b-input-group-prepend>
          <b-form-input
            type="text"
            v-model="searchBar"
            placeholder="Search Project.."
            v-b-tooltip.hover
            title="Search project by name">
          </b-form-input>
          <b-input-group-append>
            <b-dropdown class="mx-2" v-b-tooltip.hover title="Filter projects by categories">
              <template #button-content>
                <i class="fa fa-filter"></i>
              </template>
              <b-form-checkbox
                class="check"
                v-for="category in categories"
                :key="category.value"
                v-model="selectedCategories"
                :value="category.value">
                {{ category.label }}
              </b-form-checkbox>
            </b-dropdown>
            <b-button
              variant="primary"
              @click="toggleSortDirection()"
              v-b-tooltip.hover
              title="Sort projects">
              <i class="fa fa-sort-amount-down" v-if="sortDirection == 'asc'"></i>
              <i class="fa fa-sort-amount-up" v-else></i>
            </b-button>
          </b-input-group-append>
        </b-input-group>
        <div class="scrollable-menu">
          <div v-if="!searchProject.length">
            <b-card class="text-center font-weight-bolder m-2 project-card" style="font-size: 18px">
              <b-card-text>No Project Found</b-card-text>
            </b-card>
          </div>
          <div v-else>
            <b-card
              v-for="option in searchProject"
              :key="option.updated_at"
              :value="option"
              class="my-2 mx-1 project-card"
              @click="openModal(option)"
              @mouseover="hoverEffect(true, option)"
              @mouseleave="hoverEffect(false, option)"
              :class="{ selected: isMatchingOption(option) }">
              <b-card-text>
                <b-row>
                  <b-col cols="4" class="p-1">
                    <b-img-lazy
                      :src="option.image"
                      :alt="option.name"
                      rounded
                      height="70"
                      width="100"></b-img-lazy>
                  </b-col>
                  <b-col cols="8" class="p-1">
                    <div class="d-flex flex-column justify-content-between ml-2">
                      <p class="text-uppercase font-weight-bold trimmer m-0">
                        {{ nameFormat(option.name) }}
                      </p>
                      <p class="text-capitalize trimmer my-1" style="font-size: 12px">
                        {{ option.city }}, {{ option.state }}, {{ option.country }}
                      </p>
                      <p class="m-0">
                        <b-badge class="primaryBg text-uppercase">
                          {{ categoryFormat(option.category) }}
                        </b-badge>
                      </p>
                    </div>
                  </b-col>
                  <b-col
                    cols="12"
                    v-show="isMatchingOption(option) && getCurrentPath == 'datasets'">
                    <div class="d-flex align-items-center justify-content-between my-1 w-100">
                      <b-form-select
                        @change="getMetadata(option)"
                        @click.stop
                        v-model="projectSelections[`${option.id}-${option.portal}`]"
                        :options="projectData.flight_dates">
                        <template #first>
                          <b-form-select-option :value="undefined" disabled>
                            Select flight date
                          </b-form-select-option>
                        </template>
                      </b-form-select>
                      <div class="ml-5 mr-2" v-if="Object.keys(projectMetadata).length">
                        <router-link to="map" @click.stop>
                          <i
                            class="fa fa-map h2 m-0"
                            v-b-tooltip.hover.bottom
                            title="Redirect to Map"></i>
                        </router-link>
                      </div>
                    </div>
                  </b-col>
                </b-row>
              </b-card-text>
            </b-card>
          </div>
        </div>
        <b-modal
          size="lg"
          id="selectProjectModal"
          title="Project Details and Flight Date Selector"
          hide-footer
          centered
          no-close-on-esc
          no-close-on-backdrop
          hide-header-close>
          <createProject :selectedProjectProp="projectData" />
        </b-modal>
      </div>
    </div>
  </div>
</template>

<script>
import { formatCategoryName, formatProjectName, projectCategories } from '@/store/constant';
import { mapGetters } from 'vuex';

const createProject = () => import('@/components/modules/home/<USER>');
const metadataSideNav = () => import('@/components/modules/home/<USER>');

export default {
  name: 'projectSideNav',
  components: {
    createProject,
    metadataSideNav,
  },
  data() {
    return {
      searchBar: '',
      sortDirection: 'desc',
      categories: projectCategories,
      selectedCategories: [],
      projectSelections: {},
    };
  },
  computed: {
    ...mapGetters({
      projectData: 'homeStore/getProjectData',
      projectMetadata: 'homeStore/getProjectMetadata',
      projectsArray: 'homeStore/getProjectsArray',
      toggleSidebar: 'homeStore/getIsAddSidebar',
      getCurrentPath: 'homeStore/getCurrentPath',
    }),
    filteredArray() {
      if (!this.selectedCategories.length) {
        return [];
      } else if (this.selectedCategories.includes('all')) {
        return this.projectsArray;
      } else {
        return this.projectsArray.filter(project =>
          this.selectedCategories.includes(project.category)
        );
      }
    },
    sortedArray() {
      const projectArray = this.filteredArray;
      const sortDirectionMultiplier = this.sortDirection === 'asc' ? 1 : -1;
      return projectArray.sort((project1, project2) => {
        return (
          sortDirectionMultiplier * this.compareDates(project1.updated_at, project2.updated_at)
        );
      });
    },
    searchProject() {
      if (this.searchBar) {
        const searchTerm = this.searchBar.toLowerCase();
        return this.sortedArray.filter(project => project.name.toLowerCase().includes(searchTerm));
      } else {
        return this.sortedArray;
      }
    },
    categoryFormat() {
      return originalName => {
        return formatCategoryName(originalName);
      };
    },
    nameFormat() {
      return originalName => {
        return formatProjectName(originalName);
      };
    },
  },
  methods: {
    toggleMobileProjectSidebar() {
      this.$store.commit('homeStore/setToggleSidebar');
    },
    closeMobileProjectSidebar() {
      this.$store.commit('homeStore/setToggleSidebar');
    },
    isMatchingOption(option) {
      return option.id === this.projectData.id && option.portal === this.projectData.portal;
    },
    hoverEffect(isHovered, project) {
      project.isHovered = isHovered;
    },
    openModal(option) {
      if (this.getCurrentPath == 'file-upload') {
        this.$bvModal.show('selectProjectModal');
      }
      const notSameProject = this.projectData !== option;
      if (this.getCurrentPath == 'datasets' && notSameProject) {
        this.projectData.flight_date = null;
        this.$set(this.projectSelections, `${option.id}-${option.portal}`, undefined);
        this.$store.commit('homeStore/setProjectMetadata', {});
        this.$store.commit('homeStore/setIsRGB', true);
      }
      this.$store.commit('homeStore/setProjectData', option);
    },
    resetData() {
      if (this.searchBar) this.searchBar = '';
      if (this.selectedCategories.length !== this.categories.length) {
        this.selectedCategories = this.categories.map(category => category.value);
      }
      this.$store.commit('homeStore/setProjectData', {});
      this.$store.commit('homeStore/setProjectMetadata', {});
    },
    toggleSortDirection() {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    },
    compareDates(dateString1, dateString2) {
      const date1 = new Date(dateString1);
      const date2 = new Date(dateString2);
      return date1 - date2;
    },
    getMetadata(project) {
      this.projectData.flight_date = this.projectSelections[`${project.id}-${project.portal}`];
      this.$store.dispatch('homeStore/getProjectMetadata', project);
    },
  },
  async mounted() {
    this.selectedCategories = this.categories.map(category => category.value);
    try {
      await this.$store.dispatch('userStore/fetchUserDetailsApi');
      await this.$store.dispatch('homeStore/getAllProjects');
      await this.$store.dispatch('homeStore/getAllProjectsFlightDates');
    } catch (error) {
      this.$root.$emit('showToast', {
        title: 'Retrieval Error',
        message: error.message || 'An error occurred while fetching data.',
        variant: 'danger',
      });
    }
  },
};
</script>

<style scoped>
/* Mobile project sidebar toggle button */
.mobile-project-toggle {
  position: fixed;
  bottom: 1.5rem;
  right: 1rem;
  z-index: 1100;
  background: var(--primary);
  color: var(--white);
  padding: 0.75rem 1rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  min-height: 44px;
  min-width: 44px;
}

.mobile-project-toggle:hover {
  background: var(--grey);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.mobile-project-toggle i {
  font-size: 16px;
}

.mobile-project-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: var(--primary);
  font-size: 24px;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.3s ease;
  z-index: 1050;
}

.mobile-project-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

.project-sidebar {
  position: fixed;
  top: 0;
  left: 70px;
  height: 100%;
  width: 325px;
  padding: 5px;
  background: var(--white);
  transition: all 0.3s ease;
  z-index: 999;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.project-sidebar.close {
  display: none;
}
/* Mobile responsive project sidebar */
@media (max-width: 767px) {
  .project-sidebar {
    left: 0;
    width: 100%;
    height: auto;
    max-height: 50vh;
    top: auto;
    bottom: 0;
    border-radius: 1rem 1rem 0 0;
    overflow-y: auto;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 1040;
  }

  .project-sidebar.open {
    transform: translateY(0);
  }

  .project-sidebar.close {
    display: block;
    transform: translateY(100%);
  }

  .scrollable-menu {
    max-height: 40vh;
  }

  .project-card {
    margin-bottom: 0.5rem;
  }

  .header {
    font-size: 18px;
    padding: 0.5rem 0;
  }
}
.scrollable-menu {
  max-height: 85vh;
  overflow-y: auto;
}
.project-sidebar .toggle {
  position: absolute;
  top: 50%;
  right: -15px;
  min-width: unset !important;
  transform: translateY(-50%) rotate(180deg);
  height: 25px;
  width: 25px;
  background-color: var(--primary);
  color: var(--white);
  border-radius: 50%;
  border: 1px solid var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  cursor: pointer;
  transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
}
.project-sidebar .toggle:hover {
  background-color: var(--grey);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}
.project-sidebar.close .toggle {
  transform: translateY(-50%) rotate(0deg);
}
.header {
  text-align: center;
  font-size: 20px;
  font-weight: 700;
}
.check {
  padding-left: 2rem;
  margin: 0.2rem 0rem;
  color: var(--dark);
  font-weight: 500 !important;
  font-size: 14px;
  cursor: pointer;
}
.project-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 10px;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.3);
}
.project-card:hover {
  background-color: #f0f0f0;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
}
.project-card.selected {
  border-color: var(--primary);
  border-width: 3px;
}
</style>
