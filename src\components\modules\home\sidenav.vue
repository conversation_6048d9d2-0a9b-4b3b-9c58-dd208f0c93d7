<template>
  <div class="sidebar">
    <i
      class="fa fa-angle-left toggle"
      @click="$store.commit('homeStore/setToggleSidebar')"
      v-b-tooltip.hover
      title="Open Sidebar"
      v-show="!toggleSidebar"></i>
    <div class="m-1">
      <b-img :src="imageURL" alt="Logo" rounded fluid-grow thumbnail></b-img>
    </div>
    <ul class="nav-list h-100">
      <li v-for="link in links" :key="link.path">
        <router-link :to="link.path" v-b-tooltip.hover.right :title="link.header">
          <i :class="[link.icon, { active: isActive(link.path) }]"></i>
        </router-link>
      </li>
      <li v-if="getCurrentPath === 'map'"><i class="fa fa-map active"></i></li>
      <div class="down">
        <li v-for="url in urls" :key="url.path">
          <a @click="getRedirect(url.path)" v-b-tooltip.hover.right :title="url.header">
            <i :class="[url.icon]"></i>
          </a>
        </li>
      </div>
      <li class="profile">
        <a @click="logout()" v-b-tooltip.hover.right title="Logout">
          <i class="fa fa-right-from-bracket ml-2 mt-1"></i>
        </a>
      </li>
    </ul>
  </div>
</template>

<script>
import keycloakService from '@/services/keycloakService';
import { mapGetters } from 'vuex';

export default {
  name: 'sidenav',
  data() {
    return {
      isSidebarOpen: false,
      links: [
        {
          header: 'File Upload',
          icon: 'fa fa-cloud-arrow-up icon-color',
          path: 'file-upload',
        },
        {
          header: 'Datasets',
          icon: 'fa fa-database icon-color',
          path: 'datasets',
        },
      ],
      urls: [
        {
          header: 'Check Internet Speed',
          icon: 'fa fa-gauge-high icon-color',
          path: process.env.VUE_APP_SPEED_TEST,
        },
        {
          header: 'Help Center',
          icon: 'fa fa-clipboard-question icon-color',
          path: process.env.VUE_APP_HELP_CENTER,
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      toggleSidebar: 'homeStore/getIsAddSidebar',
      getCurrentPath: 'homeStore/getCurrentPath',
    }),
    isActive() {
      return path => {
        return this.$route.path.substring(1) === path;
      };
    },
    imageURL() {
      return this.$store.state.userStore.loggedInUser?.sidebar_logo;
    },
  },
  methods: {
    getRedirect(path) {
      window.open(path, '_blank');
    },
    logout() {
      this.$root.$emit('showToast', {
        title: 'Logout Successful',
        message: '',
        variant: 'success',
      });
      keycloakService.logout();
      this.$store.dispatch('userStore/logout');
      this.$router.push('/login');
    },
  },
};
</script>

<style scoped>
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  width: 70px;
  background: var(--primary);
  z-index: 1000;
  transition: all 0.5sease;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.sidebar li {
  position: relative;
  margin: 5px 0;
  list-style: none;
}
.sidebar li a {
  cursor: pointer;
  height: 100%;
  width: 100%;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.4s ease;
}
.sidebar li i:hover {
  background: var(--white);
}
.sidebar li a:hover i {
  transition: all 0.5s ease;
  color: var(--primary);
}
.sidebar li i {
  height: 50px;
  line-height: 50px;
  font-size: 18px;
  border-radius: 12px;
  color: var(--white);
  width: 50px;
  text-align: center;
}
.sidebar li.profile {
  border-top: solid 1.5px var(--white);
  position: fixed;
  height: 60px;
  width: 70px;
  left: 0;
  bottom: -5px;
  background: var(--primary);
  transition: all 0.5s ease;
}
.sidebar .toggle {
  position: absolute;
  top: 50%;
  right: -15px;
  min-width: unset !important;
  transform: translateY(-50%) rotate(180deg);
  height: 25px;
  width: 25px;
  background-color: var(--primary);
  color: var(--white);
  border-radius: 50%;
  border: 1px solid var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  cursor: pointer;
  transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
}
.sidebar .toggle:hover {
  background-color: var(--grey);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}
.sidebar.close .toggle {
  transform: translateY(-50%) rotate(0deg);
}
.active {
  color: var(--primary) !important;
  background: var(--white);
}
.down {
  bottom: 60px;
  position: fixed;
}
</style>
