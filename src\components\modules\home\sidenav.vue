<template>
  <div>
    <!-- Mobile hamburger menu button -->
    <div class="mobile-menu-toggle d-md-none" @click="toggleMobileMenu">
      <i class="fa fa-bars" v-if="!isMobileMenuOpen"></i>
      <i class="fa fa-times" v-else></i>
    </div>

    <!-- Mobile overlay -->
    <div class="mobile-overlay d-md-none" v-show="isMobileMenuOpen" @click="closeMobileMenu"></div>

    <!-- Sidebar -->
    <div class="sidebar" :class="{ 'mobile-open': isMobileMenuOpen }">
      <i
        class="fa fa-angle-left toggle d-none d-md-block"
        @click="$store.commit('homeStore/setToggleSidebar')"
        v-b-tooltip.hover
        title="Open Sidebar"
        v-show="!toggleSidebar"></i>

      <!-- Mobile close button -->
      <div class="mobile-close d-md-none" @click="closeMobileMenu">
        <i class="fa fa-times"></i>
      </div>

      <div class="logo-section">
        <b-img :src="imageURL" alt="Logo" rounded fluid-grow thumbnail></b-img>
      </div>

      <ul class="nav-list">
        <li v-for="link in links" :key="link.path">
          <router-link
            :to="link.path"
            v-b-tooltip.hover.right
            :title="link.header"
            @click="handleNavClick">
            <i :class="[link.icon, { active: isActive(link.path) }]"></i>
            <span class="nav-text d-md-none">{{ link.header }}</span>
          </router-link>
        </li>
        <li v-if="getCurrentPath === 'map'">
          <i class="fa fa-map active"></i>
          <span class="nav-text d-md-none">Map View</span>
        </li>
      </ul>

      <div class="nav-bottom">
        <ul class="nav-list">
          <li v-for="url in urls" :key="url.path">
            <a @click="getRedirect(url.path)" v-b-tooltip.hover.right :title="url.header">
              <i :class="[url.icon]"></i>
              <span class="nav-text d-md-none">{{ url.header }}</span>
            </a>
          </li>
          <li class="profile">
            <a @click="logout()" v-b-tooltip.hover.right title="Logout">
              <i class="fa fa-right-from-bracket ml-2 mt-1"></i>
              <span class="nav-text d-md-none">Logout</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import keycloakService from '@/services/keycloakService';
import { mapGetters } from 'vuex';

export default {
  name: 'sidenav',
  data() {
    return {
      isSidebarOpen: false,
      isMobileMenuOpen: false,
      links: [
        {
          header: 'File Upload',
          icon: 'fa fa-cloud-arrow-up icon-color',
          path: 'file-upload',
        },
        {
          header: 'Datasets',
          icon: 'fa fa-database icon-color',
          path: 'datasets',
        },
      ],
      urls: [
        {
          header: 'Check Internet Speed',
          icon: 'fa fa-gauge-high icon-color',
          path: process.env.VUE_APP_SPEED_TEST,
        },
        {
          header: 'Help Center',
          icon: 'fa fa-clipboard-question icon-color',
          path: process.env.VUE_APP_HELP_CENTER,
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      toggleSidebar: 'homeStore/getIsAddSidebar',
      getCurrentPath: 'homeStore/getCurrentPath',
    }),
    isActive() {
      return path => {
        return this.$route.path.substring(1) === path;
      };
    },
    imageURL() {
      return this.$store.state.userStore.loggedInUser?.sidebar_logo;
    },
  },
  methods: {
    toggleMobileMenu() {
      this.isMobileMenuOpen = !this.isMobileMenuOpen;
      // Prevent body scroll when menu is open
      if (this.isMobileMenuOpen) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }
    },
    closeMobileMenu() {
      this.isMobileMenuOpen = false;
      document.body.style.overflow = '';
    },
    handleNavClick() {
      // Close mobile menu when navigation item is clicked
      this.closeMobileMenu();
    },
    getRedirect(path) {
      window.open(path, '_blank');
      this.closeMobileMenu();
    },
    logout() {
      this.$root.$emit('showToast', {
        title: 'Logout Successful',
        message: '',
        variant: 'success',
      });
      keycloakService.logout();
      this.$store.dispatch('userStore/logout');
      this.$router.push('/login');
      this.closeMobileMenu();
    },
  },
  beforeUnmount() {
    // Clean up body overflow style
    document.body.style.overflow = '';
  },
};
</script>

<style scoped>
/* Mobile hamburger menu button */
.mobile-menu-toggle {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1100;
  background: var(--primary);
  color: var(--white);
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
  background: var(--grey);
  transform: scale(1.05);
}

.mobile-menu-toggle i {
  font-size: 20px;
}

/* Mobile overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  backdrop-filter: blur(2px);
}

/* Sidebar base styles */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  width: 70px;
  background: var(--primary);
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Mobile sidebar styles */
@media (max-width: 767px) {
  .sidebar {
    transform: translateX(-100%);
    width: 280px;
    z-index: 1060;
    padding: 1rem 0;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .sidebar .nav-list {
    width: 100%;
    padding: 0;
    margin: 0;
  }

  .sidebar li {
    margin: 0;
    width: 100%;
  }

  .sidebar li a {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    width: 100%;
    border-radius: 0;
    text-decoration: none;
    color: var(--white);
    transition: all 0.3s ease;
  }

  .sidebar li a:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
  }

  .sidebar li i {
    width: auto;
    height: auto;
    line-height: 1;
    margin-right: 1rem;
    font-size: 18px;
    border-radius: 0;
    background: transparent !important;
  }

  .nav-text {
    font-size: 16px;
    font-weight: 500;
  }

  .logo-section {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 1rem;
    width: 100%;
    text-align: center;
  }

  .nav-bottom {
    margin-top: auto;
    width: 100%;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 1rem;
  }

  .mobile-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    color: var(--white);
    font-size: 24px;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  .mobile-close:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* Desktop sidebar styles */
@media (min-width: 768px) {
  .sidebar li {
    position: relative;
    margin: 5px 0;
    list-style: none;
  }

  .sidebar li a {
    cursor: pointer;
    height: 100%;
    width: 100%;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.4s ease;
  }

  .sidebar li i:hover {
    background: var(--white);
  }

  .sidebar li a:hover i {
    transition: all 0.5s ease;
    color: var(--primary);
  }

  .sidebar li i {
    height: 50px;
    line-height: 50px;
    font-size: 18px;
    border-radius: 12px;
    color: var(--white);
    width: 50px;
    text-align: center;
  }

  .sidebar li.profile {
    border-top: solid 1.5px var(--white);
    position: fixed;
    height: 60px;
    width: 70px;
    left: 0;
    bottom: -5px;
    background: var(--primary);
    transition: all 0.5s ease;
  }

  .sidebar .toggle {
    position: absolute;
    top: 50%;
    right: -15px;
    min-width: unset !important;
    transform: translateY(-50%) rotate(180deg);
    height: 25px;
    width: 25px;
    background-color: var(--primary);
    color: var(--white);
    border-radius: 50%;
    border: 1px solid var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    cursor: pointer;
    transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
  }

  .sidebar .toggle:hover {
    background-color: var(--grey);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  }

  .sidebar.close .toggle {
    transform: translateY(-50%) rotate(0deg);
  }

  .nav-bottom {
    bottom: 60px;
    position: fixed;
  }

  .nav-text {
    display: none;
  }
}

.active {
  color: var(--primary) !important;
  background: var(--white);
}
</style>
