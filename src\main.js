import router from '@/router';
import keycloakService from '@/services/keycloakService';
import store from '@/store';
import { BootstrapVue } from 'bootstrap-vue';
import 'bootstrap-vue/dist/bootstrap-vue.css';
import 'bootstrap/dist/css/bootstrap.css';
import { Icon } from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { createApp } from 'vue';
import App from './App.vue';
import './assets/css/style.css';
import './registerServiceWorker';

// Ensure Leaflet icons work properly
delete Icon.Default.prototype._getIconUrl;
Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

// Initialize Keycloak and restore Vuex state
keycloakService
  .init()
  .then(() => {
    store.restored.then(() => {
      const app = createApp(App);
      app.provide('keycloak', keycloakService);
      app.use(BootstrapVue);
      app.use(store);
      app.use(router);
      router.isReady().then(() => {
        app.mount('#app');
      });
    });
  })
  .catch(err => {
    console.error('Keycloak initialization failed', err);
  });
