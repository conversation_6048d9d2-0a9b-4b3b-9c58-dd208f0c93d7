import keycloakService from '@/services/keycloakService';
import { createRouter, createWebHistory } from 'vue-router';

const fileUploadPage = () => import('../views/fileUploadPage.vue');
const uploadedFilesPage = () => import('../views/uploadedFilesPage.vue');
const mapPage = () => import('../views/mapPage.vue');
const notFoundPage = () => import('../views/notFoundPage.vue');
const login = () => import('@/components/modules/user/login.vue');

const router = createRouter({
  history: createWebHistory('/'),
  routes: [
    {
      path: '/',
      redirect: '/file-upload',
    },
    {
      path: '/file-upload',
      component: fileUploadPage,
    },
    {
      path: '/datasets',
      component: uploadedFilesPage,
    },
    {
      path: '/map',
      component: mapPage,
    },
    {
      path: '/login',
      component: login,
      meta: {
        public: true, // Allow access to even if not logged in
        onlyWhenLoggedOut: true,
      },
    },
    { path: '/:pathMatch(.*)*', component: notFoundPage },
  ],
});

router.beforeEach((to, from, next) => {
  const isPublic = to.matched.some(record => record.meta.public);
  const onlyWhenLoggedOut = to.matched.some(record => record.meta.onlyWhenLoggedOut);
  const urlParams = new URLSearchParams(window.location.search);

  const encodedToken = urlParams.get('customToken');
  if (encodedToken) {
    const decodedData = atob(decodeURIComponent(encodedToken));
    keycloakService.setCustomToken(JSON.parse(decodedData));
  }

  const loggedIn = keycloakService.isLoggedIn();
  if (!isPublic && !loggedIn) return next({ path: '/login' });

  // Do not allow user to visit login page or register page if they are logged in
  if (loggedIn && onlyWhenLoggedOut) return next('/');

  next();
});

router.afterEach(() => {
  if (window.location.hash) {
    const cleanPath = window.location.pathname + window.location.search;
    history.replaceState(null, null, cleanPath);
  }
});
export default router;
