import { STORAGE_KEYS } from '@/store/constant';
import { AES, enc, lib, mode, pad } from 'crypto-js';
import { reactive } from 'vue';

const state = reactive({
  masterKey: enc.Hex.parse(process.env.VUE_APP_MASTER_KEY),
  keyBase64: '',
  ivHex: '',
});

export const generateKey = async () => {
  state.keyBase64 = retrieveKey(localStorage.getItem(STORAGE_KEYS.KEY_KEY));
  state.ivHex = retrieveKey(localStorage.getItem(STORAGE_KEYS.IV_KEY));
  if (!state.keyBase64 || !state.ivHex) {
    state.keyBase64 = enc.Base64.stringify(lib.WordArray.random(32));
    state.ivHex = enc.Hex.stringify(lib.WordArray.random(16));

    storeKey(STORAGE_KEYS.KEY_KEY, state.keyBase64);
    storeKey(STORAGE_KEYS.IV_KEY, state.ivHex);
  }
};

export const retrieveKey = encryptedData => {
  if (!encryptedData) return null;
  try {
    const decryptedData = AES.decrypt(encryptedData, state.masterKey.toString()).toString(enc.Utf8);
    return decryptedData;
  } catch (error) {
    console.error('Error decrypting data:', error);
    return null;
  }
};

export const storeKey = (storageKey, data) => {
  try {
    const encryptedData = AES.encrypt(data, state.masterKey.toString()).toString();
    localStorage.setItem(storageKey, encryptedData);
  } catch (error) {
    console.error('Error storing data:', error);
  }
};

export const encryptData = data => {
  return AES.encrypt(JSON.stringify(data), enc.Base64.parse(state.keyBase64), {
    iv: enc.Hex.parse(state.ivHex),
    padding: pad.Pkcs7,
    mode: mode.CBC,
  }).toString();
};

export const decryptData = encryptedData => {
  const bytes = AES.decrypt(encryptedData, enc.Base64.parse(state.keyBase64), {
    iv: enc.Hex.parse(state.ivHex),
    padding: pad.Pkcs7,
    mode: mode.CBC,
  });
  return JSON.parse(bytes.toString(enc.Utf8));
};

export const encryptString = message => {
  const iv = lib.WordArray.random(16);
  const encrypted = AES.encrypt(enc.Utf8.parse(message), state.masterKey, {
    iv: iv,
    padding: pad.Pkcs7,
    mode: mode.CBC,
  });
  return `${encrypted.toString()}:${iv.toString(enc.Base64)}`;
};

export const decryptString = encryptedMessage => {
  const [ciphertext, iv] = encryptedMessage.split(':');
  const decrypted = AES.decrypt(ciphertext, state.masterKey, {
    iv: enc.Base64.parse(iv),
    padding: pad.Pkcs7,
    mode: mode.CBC,
  });
  return decrypted.toString(enc.Utf8);
};
