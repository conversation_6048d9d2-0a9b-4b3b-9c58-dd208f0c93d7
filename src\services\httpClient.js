import router from '@/router';
import keycloakService from '@/services/keycloakService';
import store from '@/store';
import axios from 'axios';

// Helper to create a new Axios instance
const createHttpClient = baseURL => {
  return axios.create({ baseURL });
};

const addInterceptors = instance => {
  // Request Interceptor
  instance.interceptors.request.use(
    config => {
      const token = keycloakService.getToken();
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      } else {
        console.warn('Authorization token is missing.');
      }
      return config;
    },
    error => {
      console.error('Request error:', error);
      return Promise.reject(error);
    }
  );

  // Response Interceptor
  instance.interceptors.response.use(
    response => response,
    error => {
      if (error.response) {
        const { status } = error.response;
        if (status === 401 || status === 403) {
          keycloakService.logout();
          store.dispatch('userStore/logout');
          router.push('/login');
        }
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Unexpected error:', error.message);
      }
      return Promise.reject(error);
    }
  );
};

// Create Axios instances
const measureHttpClient = createHttpClient(process.env.VUE_APP_API_MEASURE_URL);
const carnotHttpClient = createHttpClient(process.env.VUE_APP_API_CARNOT_URL);
const dataHttpClient = createHttpClient(process.env.VUE_APP_API_FTP_URL);

// Add interceptors
addInterceptors(measureHttpClient);
addInterceptors(carnotHttpClient);
addInterceptors(dataHttpClient);

export { carnotHttpClient, dataHttpClient, measureHttpClient };
