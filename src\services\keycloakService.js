import { measureHttpClient } from '@/services/httpClient';
import { REFRESH_INTERVAL, STORAGE_KEYS } from '@/store/constant';
import Keycloak from 'keycloak-js';
import { reactive } from 'vue';

const state = reactive({
  keycloak: null,
  customToken: null,
  tokenRefreshInterval: null,
  keycloakConfig: {
    url: process.env.VUE_APP_KEYCLOAK_URL,
    realm: process.env.VUE_APP_KEYCLOAK_REALM,
    clientId: process.env.VUE_APP_KEYCLOAK_CLIENTID,
  },
});

const keycloakService = {
  async init() {
    state.keycloak = new Keycloak(state.keycloakConfig);
    try {
      const authenticated = await state.keycloak.init({
        onLoad: 'check-sso',
        flow: 'hybrid',
        checkLoginIframe: false,
      });
      if (authenticated || this.getCustomToken()) {
        await this.startTokenRefresh();
      }
      return authenticated;
    } catch (error) {
      console.error('Error initializing Keycloak:', error);
      return false;
    }
  },
  async loginWithEmail(credentials) {
    try {
      const { data } = await measureHttpClient.post('accounts/login/', credentials);
      if (data?.token) {
        this.setCustomToken(data.token);
        await this.startTokenRefresh();
      }
      return data;
    } catch (error) {
      throw error.response?.data || error;
    }
  },
  async getUserInfo() {
    const userInfo = await state.keycloak.loadUserProfile();
    const role = state.keycloak.realmAccess.roles[0];
    return { userInfo, role };
  },
  async refreshToken() {
    return this.getCustomToken() ? this.refreshCustomToken() : this.refreshKeycloakToken();
  },
  async refreshCustomToken() {
    const token = this.getCustomToken();
    if (!token?.refresh_token) {
      console.error('No refresh token available');
      return false;
    }
    try {
      const { data } = await measureHttpClient.post('accounts/refresh_token/', {
        refresh_token: token.refresh_token,
      });
      if (data?.token) {
        this.setCustomToken(data.token);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  },
  async refreshKeycloakToken() {
    try {
      await state.keycloak.updateToken(60);
      return true;
    } catch (error) {
      console.error('Keycloak token refresh failed:', error);
      return false;
    }
  },
  setTokenRefreshTime() {
    localStorage.setItem(STORAGE_KEYS.TOKEN_REFRESH, String(Date.now() + REFRESH_INTERVAL));
  },
  getTokenRefreshTime() {
    return parseInt(localStorage.getItem(STORAGE_KEYS.TOKEN_REFRESH) || '0');
  },
  async startTokenRefresh() {
    this.setTokenRefreshTime();
    this.stopTokenRefresh();
    const remainingTime = Math.max(0, this.getTokenRefreshTime() - Date.now());
    if (remainingTime < REFRESH_INTERVAL && remainingTime > 0) {
      await this.refreshToken();
    }
    state.tokenRefreshInterval = setInterval(async () => {
      const success = await this.refreshToken();
      if (success) this.setTokenRefreshTime();
    }, REFRESH_INTERVAL);
  },
  stopTokenRefresh() {
    if (state.tokenRefreshInterval) {
      clearInterval(state.tokenRefreshInterval);
      state.tokenRefreshInterval = null;
    }
    localStorage.removeItem(STORAGE_KEYS.TOKEN_REFRESH);
  },
  setCustomToken(token) {
    localStorage.setItem(STORAGE_KEYS.CUSTOM_TOKEN, JSON.stringify(token));
    state.customToken = token;
  },
  getCustomToken() {
    if (!state.customToken) {
      try {
        const stored = localStorage.getItem(STORAGE_KEYS.CUSTOM_TOKEN);
        state.customToken = stored ? JSON.parse(stored) : null;
      } catch (error) {
        console.error('Error parsing stored token:', error);
        state.customToken = null;
      }
    }
    return state.customToken;
  },
  clearCustomToken() {
    localStorage.removeItem(STORAGE_KEYS.CUSTOM_TOKEN);
    state.customToken = null;
  },
  isLoggedIn() {
    return !!this.getCustomToken() || state.keycloak?.authenticated;
  },
  getToken() {
    const customToken = this.getCustomToken();
    return customToken?.access_token || state.keycloak?.token;
  },
  loginWithSocial(provider) {
    return state.keycloak.login({ idpHint: provider });
  },
  async logout() {
    this.stopTokenRefresh();
    if (this.getCustomToken()) {
      this.clearCustomToken();
      return;
    }
    return state.keycloak.logout();
  },
};

export default keycloakService;
