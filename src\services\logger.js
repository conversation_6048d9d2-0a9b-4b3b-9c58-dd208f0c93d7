import { dataHttpClient } from '@/services/httpClient';
import store from '@/store';
import log from 'loglevel';

const timestampOptions = {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  hour: 'numeric',
  minute: 'numeric',
  second: 'numeric',
  hour12: true,
  timeZone: 'Asia/Kolkata',
};

async function logToServer(level, ...args) {
  try {
    const logData = {
      level,
      message: args.join(' '),
      user: store.state.userStore.loggedInUser.name,
      timestamp: new Date().toLocaleString('en-IN', timestampOptions),
    };
    await dataHttpClient.post('logs/', logData);
  } catch (error) {
    console.error('Error sending log to backend:', error);
  }
}

log.info = (...args) => {
  logToServer('info', ...args);
};

export default log;
