export const SESSION_TIMEOUT = 2 * 3600000; // Session timeout: 2 hours in milliseconds
export const REFRESH_INTERVAL = 240 * 1000; // 4 minutes

export const STORAGE_KEYS = {
  TOKEN_REFRESH: 'tokenRefreshTime',
  CUSTOM_TOKEN: 'customToken',
  KEY_KEY: 'key',
  IV_KEY: 'iv',
};

export const IMAGE_TYPE = {
  RGB: 'RGB',
  THERMAL: 'THERMAL',
};

export const fileFormats = {
  RGB: ['.jpeg', '.png', '.jpg'],
  KML: ['.kml', '.kmz', '.zip', '.7z'],
  CAD: ['.dwg', '.dxf', '.pdf', '.zip', '.7z'],
  GCP: ['.kml', '.kmz', '.zip', '.7z', '.csv', '.xlsx', '.xls'],
  SHAPEFILE: ['.shp', '.zip', '.dbf', '.cpg', '.prj', '.qmd', '.sbn', '.shp', '.shx'],
  LIDAR: [
    '.mrk',
    '.rpt',
    '.clc',
    '.cli',
    '.dbg',
    '.imu',
    '.ldr',
    '.ldrt',
    '.rpos',
    '.rtb',
    '.rtk',
    '.rts',
    '.rtl',
    '.sig',
  ],
};

export const projectCategories = [
  { value: 'thermography', label: 'Thermography' },
  { value: 'topography', label: 'Topography' },
  { value: 'grading', label: 'Grading' },
  { value: 'due_diligence', label: 'Due Diligence' },
  { value: 'vegetation', label: 'Vegetation' },
  { value: 'cpm_cqm', label: 'CPM & CQM' },
];

export const formatCustomDate = date => {
  const formatter = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  });
  const parts = formatter.formatToParts(date);

  const day = parts.find(part => part.type === 'day').value;
  const month = parts.find(part => part.type === 'month').value;
  const year = parts.find(part => part.type === 'year').value;
  const hour = parts.find(part => part.type === 'hour').value;
  const minute = parts.find(part => part.type === 'minute').value;
  const second = parts.find(part => part.type === 'second').value;
  const period = parts.find(part => part.type === 'dayPeriod').value;

  return `${day}-${month}-${year}, ${hour}:${minute}:${second} ${period}`;
};

export function formatProjectName(name) {
  return name?.toUpperCase().replace(/[_-]/g, ' ');
}

export function formatCategoryName(name) {
  return name?.toUpperCase().replace(/_/g, ' & ');
}

export const handleResponse = response => {
  const { data } = response;
  if (data && data.status === 'success') {
    return data;
  }
  throw new Error(data.message || 'An error occurred');
};

export const handleError = error => {
  console.error('API Error:', error);
  return Promise.reject(error.response?.data || { message: 'Unknown error occurred' });
};
