import { carnotHttpClient, dataHttpClient, measureHttpClient } from '@/services/httpClient';
import { handleError, handleResponse, IMAGE_TYPE } from '@/store/constant';

const getInitialState = () => ({
  projectsArray: [],
  projectSelected: {},
  projectMetadata: {},
  projectsFlightDatesList: [],
  isAddSidebar: true,
  currentPath: '',
  weatherByProjects: {},
  metadataByProjects: {},
  isFlightDateFetched: false,
  isRGB: true,
});

const state = getInitialState();

const getters = {
  getProjectsArray: state => state.projectsArray,
  getProjectData: state => state.projectSelected,
  getProjectMetadata: state => state.projectMetadata,
  getIsAddSidebar: state => state.isAddSidebar,
  getCurrentPath: state => state.currentPath,
  isThermography: state => state.projectSelected?.category == 'thermography',
  getDisplayedImages: state =>
    (state.projectMetadata?.image_metadata ?? []).filter(image =>
      state.isRGB ? image.type === IMAGE_TYPE.RGB : image.type === IMAGE_TYPE.THERMAL
    ),
};

const actions = {
  async getAllProjects({ commit, state, dispatch }) {
    if (state.projectsArray.length) return;

    dispatch('toggleSpinner', null, { root: true });
    let combinedData = [];
    try {
      const response = await measureHttpClient.get('project/get_project_info');
      const measureData = handleResponse(response);
      combinedData.push(...measureData.data);
    } catch (error) {
      console.error('Measure API failed:', error);
    }
    try {
      const response = await carnotHttpClient.get('project/get_project_info');
      const carnotData = handleResponse(response);
      combinedData.push(...carnotData.data);
    } catch (error) {
      console.error('Carnot API failed:', error);
    }
    commit('setProjectsArray', combinedData);
    dispatch('toggleSpinner', null, { root: true });
  },
  async getAllProjectsFlightDates({ commit, state, dispatch }) {
    if (state.projectsFlightDatesList.length || state.isFlightDateFetched) return;

    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await dataHttpClient.get(`metadata/flight_dates`);
      const flightData = response.data?.data;

      const flightDatesMap = new Map(
        flightData.map(item => [`${item.portal}-${item.project_id}`, item.flight_dates])
      );
      const updateProjectsArray = state.projectsArray.map(item => {
        const flight_dates = flightDatesMap.get(`${item.portal}-${item.id}`) || [];
        return { ...item, flight_dates };
      });
      state.isFlightDateFetched = true;

      commit('setProjectsFlightDatesList', flightData);
      commit('setProjectsArray', updateProjectsArray);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async getProjectMetadata({ commit, state, dispatch }, data) {
    const cacheKey = `${data.portal}/${data.id}/${data.flight_date}`;

    const cachedData = state.metadataByProjects?.[cacheKey];
    if (cachedData) {
      commit('setProjectMetadata', cachedData);
      return Promise.resolve(cachedData);
    }

    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await dataHttpClient.get(`metadata/${cacheKey}`);
      const metaData = response.data?.data;

      for (let i = 0; i < metaData.image_metadata.length; i++) {
        const item = metaData.image_metadata[i];
        const segments = item.path.split('/');
        const rgbIndex = segments.indexOf('RGB');
        const thermalIndex = segments.indexOf('THERMAL');
        let blockIndex = rgbIndex !== -1 ? rgbIndex : thermalIndex;
        if (blockIndex !== -1 && blockIndex + 1 < segments.length) {
          item.block = decodeURIComponent(segments[blockIndex + 1])
            .toUpperCase()
            .replace(/[.\s-]/g, '_');
          item.type = segments[blockIndex];
        } else {
          item.block = '';
          item.type = '';
        }
        const dateParts = item.datetime.split(' ');
        if (dateParts.length === 2) {
          let [year, month, day] = dateParts[0].split(':');
          let [hour, minute, second] = dateParts[1].split(':');
          hour = parseInt(hour, 10);
          const period = hour >= 12 ? 'PM' : 'AM';
          hour = hour % 12 || 12;
          item.datetime = `${day}/${month}/${year} ${hour}:${minute}:${second} ${period}`;
        }
      }

      commit('cacheMetadataByProjects', { id: cacheKey, data: metaData });
      commit('setProjectMetadata', metaData);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async getWeatherData({ commit, state, dispatch }, data) {
    const cacheKey = `${data[0]}_${data[1]}`;
    const cachedWeather = state.weatherByProjects[cacheKey];
    if (cachedWeather) {
      return Promise.resolve({ data: cachedWeather });
    }

    dispatch('toggleSpinner', null, { root: true });
    try {
      const params = `?lat=${data[0]}&lon=${data[1]}`;
      const response = await measureHttpClient.get(`project/get_weather/${params}`);
      const weatherData = response.data?.data;

      commit('cacheWeatherByProjects', { id: cacheKey, data: weatherData });
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  resetModule({ commit }) {
    commit('resetModule');
  },
};

const mutations = {
  setToggleSidebar(state) {
    state.isAddSidebar = !state.isAddSidebar;
  },
  setCurrentPath(state, data) {
    state.currentPath = data;
  },
  setIsRGB(state, data) {
    state.isRGB = data;
  },
  setProjectsArray(state, data) {
    state.projectsArray = data;
  },
  setProjectData(state, data) {
    state.projectSelected = data;
  },
  setProjectsFlightDatesList(state, data) {
    state.projectsFlightDatesList = data;
  },
  setProjectMetadata(state, data) {
    state.projectMetadata = data;
  },
  cacheMetadataByProjects(state, { id, data }) {
    state.metadataByProjects = { ...state.metadataByProjects, [id]: data };
  },
  cacheWeatherByProjects(state, { id, data }) {
    state.weatherByProjects = { ...state.weatherByProjects, [id]: data };
  },
  resetModule(state) {
    Object.assign(state, getInitialState());
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
