import { carnotHttpClient, measureHttpClient } from '@/services/httpClient';
import keycloakService from '@/services/keycloakService';

function mapUserDetails(userInfo, role) {
  const extractDomainFromEmail = email => email.match(/@([^.]+)\./)?.[1] || '';

  return {
    name: `${userInfo.firstName?.trim() || userInfo.first_name?.trim()} ${
      userInfo.lastName?.trim() || userInfo.last_name?.trim()
    }`,
    email: userInfo.email,
    id: userInfo.id,
    role: role,
    bucket: `ftp-${extractDomainFromEmail(userInfo.email)}`,
    sidebar_logo: userInfo.sidebar_logo || process.env.VUE_APP_LOGO_SIDEBAR,
  };
}

const getInitialState = () => ({
  loggedInUser: {},
  flowType: '',
});

const state = getInitialState();

const actions = {
  async loginWithEmail({ commit }, { data, flowType }) {
    commit('setFlowType', flowType);
    try {
      const httpClient = flowType === 'carnot' ? carnotHttpClient : measureHttpClient;
      const response = await httpClient.post('accounts/login/', data);

      if (response?.data?.token) {
        keycloakService.setCustomToken(response.data.token);
        return response.data;
      }
    } catch (error) {
      console.error('Failed to perform user login:', error);
      throw error.response?.data || error;
    }
  },
  async fetchUserDetailsApi({ commit, state, dispatch }) {
    if (state.loggedInUser && state.loggedInUser.id) return;

    dispatch('toggleSpinner', null, { root: true });
    try {
      const token = keycloakService.getCustomToken();
      let user;
      if (!token) {
        const { userInfo, role } = await keycloakService.getUserInfo();
        user = mapUserDetails(userInfo, role);
      } else {
        const httpClient = state.flowType === 'carnot' ? carnotHttpClient : measureHttpClient;
        const response = await httpClient.get('accounts/user_details/');
        const userInfo = response.data?.data;
        user = mapUserDetails(userInfo, userInfo?.role);
      }
      commit('setLoggedInUser', user);
    } catch (error) {
      console.error('Failed to fetch user details:', error);
      throw error;
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async logout({ commit, dispatch }) {
    dispatch('homeStore/resetModule', null, { root: true });
    commit('resetModule');
    await dispatch('resetRoot', null, { root: true });
  },
  resetModule({ commit }) {
    commit('resetModule');
  },
};

const mutations = {
  setLoggedInUser(state, userData) {
    state.loggedInUser = userData;
  },
  setFlowType(state, flowType) {
    state.flowType = flowType;
  },
  resetModule(state) {
    Object.assign(state, getInitialState());
  },
};

export default {
  namespaced: true,
  state,
  actions,
  mutations,
};
