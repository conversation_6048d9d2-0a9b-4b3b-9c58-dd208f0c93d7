<template>
  <div>
    <sidenav />
    <projectSideNav />
    <airmap />
  </div>
</template>

<script>
const airmap = () => import('@/components/modules/home/<USER>');
const sidenav = () => import('@/components/modules/home/<USER>');
const projectSideNav = () => import('@/components/modules/home/<USER>');
import { mapGetters } from 'vuex';

export default {
  name: 'mapPage',
  components: {
    sidenav,
    airmap,
    projectSideNav,
  },
  computed: {
    ...mapGetters({
      projectData: 'homeStore/getProjectData',
      projectMetadata: 'homeStore/getProjectMetadata',
    }),
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      const project = vm.projectData;
      const metadata = vm.projectMetadata;

      const isValidProject =
        Object.keys(project).length &&
        Object.keys(metadata).length &&
        project.portal === metadata.portal &&
        project.id === metadata.project_id;

      if (isValidProject) {
        next(); // Proceed if valid
      } else {
        vm.$router.push('/datasets'); // Redirect to datasets if invalid
      }
    });
  },
};
</script>

<style scoped></style>
