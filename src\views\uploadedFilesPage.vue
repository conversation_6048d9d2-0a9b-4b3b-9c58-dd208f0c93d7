<template>
  <div>
    <sidenav />
    <projectSideNav />
    <navbar />
    <filesData />
  </div>
</template>

<script>
const filesData = () => import('@/components/modules/home/<USER>');
const sidenav = () => import('@/components/modules/home/<USER>');
const navbar = () => import('@/components/modules/home/<USER>');
const projectSideNav = () => import('@/components/modules/home/<USER>');

export default {
  name: 'uploadedFilesPage',
  components: {
    sidenav,
    navbar,
    filesData,
    projectSideNav,
  },
};
</script>

<style scoped></style>
